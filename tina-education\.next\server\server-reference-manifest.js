self.__RSC_SERVER_MANIFEST="{\n  \"node\": {\n    \"00935df8bdccbccf145033aaf52b70034d7d12e3f4\": {\n      \"workers\": {\n        \"app/dashboard/notifications/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cactions%5C%5Cnotifications.ts%22%2C%5B%7B%22id%22%3A%2200935df8bdccbccf145033aaf52b70034d7d12e3f4%22%2C%22exportedName%22%3A%22markAllNotificationsAsRead%22%7D%2C%7B%22id%22%3A%2200d35417c9febe27ada152fd1ba54552bc0ba002e1%22%2C%22exportedName%22%3A%22getUnreadNotificationCount%22%7D%2C%7B%22id%22%3A%22406049ee3a2374be5a6c6fb9d2b3fa3ed10a39c8e9%22%2C%22exportedName%22%3A%22markNotificationAsRead%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/dashboard/notifications/page\": \"action-browser\"\n      }\n    },\n    \"00d35417c9febe27ada152fd1ba54552bc0ba002e1\": {\n      \"workers\": {\n        \"app/dashboard/notifications/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cactions%5C%5Cnotifications.ts%22%2C%5B%7B%22id%22%3A%2200935df8bdccbccf145033aaf52b70034d7d12e3f4%22%2C%22exportedName%22%3A%22markAllNotificationsAsRead%22%7D%2C%7B%22id%22%3A%2200d35417c9febe27ada152fd1ba54552bc0ba002e1%22%2C%22exportedName%22%3A%22getUnreadNotificationCount%22%7D%2C%7B%22id%22%3A%22406049ee3a2374be5a6c6fb9d2b3fa3ed10a39c8e9%22%2C%22exportedName%22%3A%22markNotificationAsRead%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/dashboard/notifications/page\": \"action-browser\"\n      }\n    },\n    \"406049ee3a2374be5a6c6fb9d2b3fa3ed10a39c8e9\": {\n      \"workers\": {\n        \"app/dashboard/notifications/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cactions%5C%5Cnotifications.ts%22%2C%5B%7B%22id%22%3A%2200935df8bdccbccf145033aaf52b70034d7d12e3f4%22%2C%22exportedName%22%3A%22markAllNotificationsAsRead%22%7D%2C%7B%22id%22%3A%2200d35417c9febe27ada152fd1ba54552bc0ba002e1%22%2C%22exportedName%22%3A%22getUnreadNotificationCount%22%7D%2C%7B%22id%22%3A%22406049ee3a2374be5a6c6fb9d2b3fa3ed10a39c8e9%22%2C%22exportedName%22%3A%22markNotificationAsRead%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/dashboard/notifications/page\": \"action-browser\"\n      }\n    }\n  },\n  \"edge\": {},\n  \"encryptionKey\": \"process.env.NEXT_SERVER_ACTIONS_ENCRYPTION_KEY\"\n}"