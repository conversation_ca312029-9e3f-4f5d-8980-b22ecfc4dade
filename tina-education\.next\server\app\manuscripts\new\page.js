/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/manuscripts/new/page";
exports.ids = ["app/manuscripts/new/page"];
exports.modules = {

/***/ "(rsc)/./app/components/NotificationToast.tsx":
/*!**********************************************!*\
  !*** ./app/components/NotificationToast.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\NotificationToast.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\NotificationToast.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./app/components/footer.tsx":
/*!***********************************!*\
  !*** ./app/components/footer.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\footer.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./app/components/home_nav.tsx":
/*!*************************************!*\
  !*** ./app/components/home_nav.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home_nav.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\home_nav.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./app/components/submit.tsx":
/*!***********************************!*\
  !*** ./app/components/submit.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\submit.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\submit.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./app/context/AuthProvider.tsx":
/*!**************************************!*\
  !*** ./app/context/AuthProvider.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\context\\\\AuthProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\context\\AuthProvider.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"249b9d197b61\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYmljaGVcXFRpbmEgRWR1Y2F0aW9uLm9yZ1xcUmV2aWV3IFJlcXVlc3QgYmFja2VuZFxcQVJSUy1OZXh0SlNcXHRpbmEtZWR1Y2F0aW9uXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiMjQ5YjlkMTk3YjYxXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _context_AuthProvider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./context/AuthProvider */ \"(rsc)/./app/context/AuthProvider.tsx\");\n/* harmony import */ var _components_home_nav__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./components/home_nav */ \"(rsc)/./app/components/home_nav.tsx\");\n/* harmony import */ var _components_footer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./components/footer */ \"(rsc)/./app/components/footer.tsx\");\n/* harmony import */ var _components_NotificationToast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./components/NotificationToast */ \"(rsc)/./app/components/NotificationToast.tsx\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\n\n\n\n\n\n\nconst metadata = {\n    title: \"Tina Education\",\n    description: \"Generated by create next app\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_6___default().variable)} ${(next_font_google_target_css_path_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_7___default().variable)} antialiased min-h-screen flex flex-col`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_AuthProvider__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_home_nav__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\layout.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\layout.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_footer__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\layout.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NotificationToast__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\layout.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\layout.tsx\",\n                lineNumber: 34,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\layout.tsx\",\n            lineNumber: 31,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\layout.tsx\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/manuscripts/new/page.tsx":
/*!**************************************!*\
  !*** ./app/manuscripts/new/page.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Administrator)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(rsc)/./node_modules/next-auth/react.js\");\n/* harmony import */ var _components_submit__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../components/submit */ \"(rsc)/./app/components/submit.tsx\");\n\n\n\nfunction Administrator() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_1__.SessionProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_submit__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\manuscripts\\\\new\\\\page.tsx\",\n            lineNumber: 7,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\manuscripts\\\\new\\\\page.tsx\",\n        lineNumber: 6,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbWFudXNjcmlwdHMvbmV3L3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUFrRDtBQUNMO0FBRTlCLFNBQVNFO0lBQ3RCLHFCQUNFLDhEQUFDRiw0REFBZUE7a0JBQ2QsNEVBQUNDLDBEQUFNQTs7Ozs7Ozs7OztBQUdiIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGJpY2hlXFxUaW5hIEVkdWNhdGlvbi5vcmdcXFJldmlldyBSZXF1ZXN0IGJhY2tlbmRcXEFSUlMtTmV4dEpTXFx0aW5hLWVkdWNhdGlvblxcYXBwXFxtYW51c2NyaXB0c1xcbmV3XFxwYWdlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBTZXNzaW9uUHJvdmlkZXIgfSBmcm9tIFwibmV4dC1hdXRoL3JlYWN0XCI7XHJcbmltcG9ydCBTdWJtaXQgZnJvbSBcIi4uLy4uL2NvbXBvbmVudHMvc3VibWl0XCI7XHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBBZG1pbmlzdHJhdG9yKCkge1xyXG4gIHJldHVybiAoXHJcbiAgICA8U2Vzc2lvblByb3ZpZGVyPlxyXG4gICAgICA8U3VibWl0IC8+XHJcbiAgICA8L1Nlc3Npb25Qcm92aWRlcj5cclxuICApO1xyXG59XHJcbiJdLCJuYW1lcyI6WyJTZXNzaW9uUHJvdmlkZXIiLCJTdWJtaXQiLCJBZG1pbmlzdHJhdG9yIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/manuscripts/new/page.tsx\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fmanuscripts%2Fnew%2Fpage&page=%2Fmanuscripts%2Fnew%2Fpage&appPaths=%2Fmanuscripts%2Fnew%2Fpage&pagePath=private-next-app-dir%2Fmanuscripts%2Fnew%2Fpage.tsx&appDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fmanuscripts%2Fnew%2Fpage&page=%2Fmanuscripts%2Fnew%2Fpage&appPaths=%2Fmanuscripts%2Fnew%2Fpage&pagePath=private-next-app-dir%2Fmanuscripts%2Fnew%2Fpage.tsx&appDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/manuscripts/new/page.tsx */ \"(rsc)/./app/manuscripts/new/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'manuscripts',\n        {\n        children: [\n        'new',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\manuscripts\\\\new\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\manuscripts\\\\new\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/manuscripts/new/page\",\n        pathname: \"/manuscripts/new\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fmanuscripts%2Fnew%2Fpage&page=%2Fmanuscripts%2Fnew%2Fpage&appPaths=%2Fmanuscripts%2Fnew%2Fpage&pagePath=private-next-app-dir%2Fmanuscripts%2Fnew%2Fpage.tsx&appDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccomponents%5C%5Cfooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccomponents%5C%5Chome_nav.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccomponents%5C%5CNotificationToast.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccontext%5C%5CAuthProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccomponents%5C%5Cfooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccomponents%5C%5Chome_nav.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccomponents%5C%5CNotificationToast.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccontext%5C%5CAuthProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/components/footer.tsx */ \"(rsc)/./app/components/footer.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/components/home_nav.tsx */ \"(rsc)/./app/components/home_nav.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/components/NotificationToast.tsx */ \"(rsc)/./app/components/NotificationToast.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/context/AuthProvider.tsx */ \"(rsc)/./app/context/AuthProvider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccomponents%5C%5Cfooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccomponents%5C%5Chome_nav.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccomponents%5C%5CNotificationToast.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccontext%5C%5CAuthProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccomponents%5C%5Csubmit.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext-auth%5C%5Creact.js%22%2C%22ids%22%3A%5B%22SessionProvider%22%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccomponents%5C%5Csubmit.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext-auth%5C%5Creact.js%22%2C%22ids%22%3A%5B%22SessionProvider%22%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/components/submit.tsx */ \"(rsc)/./app/components/submit.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-auth/react.js */ \"(rsc)/./node_modules/next-auth/react.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2JpY2hlJTVDJTVDVGluYSUyMEVkdWNhdGlvbi5vcmclNUMlNUNSZXZpZXclMjBSZXF1ZXN0JTIwYmFja2VuZCU1QyU1Q0FSUlMtTmV4dEpTJTVDJTVDdGluYS1lZHVjYXRpb24lNUMlNUNhcHAlNUMlNUNjb21wb25lbnRzJTVDJTVDc3VibWl0LnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDYmljaGUlNUMlNUNUaW5hJTIwRWR1Y2F0aW9uLm9yZyU1QyU1Q1JldmlldyUyMFJlcXVlc3QlMjBiYWNrZW5kJTVDJTVDQVJSUy1OZXh0SlMlNUMlNUN0aW5hLWVkdWNhdGlvbiU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQtYXV0aCU1QyU1Q3JlYWN0LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyU2Vzc2lvblByb3ZpZGVyJTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxrS0FBd0w7QUFDeEw7QUFDQSw4S0FBc00iLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcImRlZmF1bHRcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxiaWNoZVxcXFxUaW5hIEVkdWNhdGlvbi5vcmdcXFxcUmV2aWV3IFJlcXVlc3QgYmFja2VuZFxcXFxBUlJTLU5leHRKU1xcXFx0aW5hLWVkdWNhdGlvblxcXFxhcHBcXFxcY29tcG9uZW50c1xcXFxzdWJtaXQudHN4XCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJTZXNzaW9uUHJvdmlkZXJcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxiaWNoZVxcXFxUaW5hIEVkdWNhdGlvbi5vcmdcXFxcUmV2aWV3IFJlcXVlc3QgYmFja2VuZFxcXFxBUlJTLU5leHRKU1xcXFx0aW5hLWVkdWNhdGlvblxcXFxub2RlX21vZHVsZXNcXFxcbmV4dC1hdXRoXFxcXHJlYWN0LmpzXCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccomponents%5C%5Csubmit.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext-auth%5C%5Creact.js%22%2C%22ids%22%3A%5B%22SessionProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__":
/*!**********************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ ***!
  \**********************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9hcHAvZmF2aWNvbi5pY28/X19uZXh0X21ldGFkYXRhX18iLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUEsRUFBaUY7O0FBRWpGLEVBQUUsaUVBQWU7QUFDakIsdUJBQXVCO0FBQ3ZCLHFCQUFxQiw4RkFBbUI7O0FBRXhDO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxiaWNoZVxcVGluYSBFZHVjYXRpb24ub3JnXFxSZXZpZXcgUmVxdWVzdCBiYWNrZW5kXFxBUlJTLU5leHRKU1xcdGluYS1lZHVjYXRpb25cXGFwcFxcZmF2aWNvbi5pY28/X19uZXh0X21ldGFkYXRhX18iXSwic291cmNlc0NvbnRlbnQiOlsiICBpbXBvcnQgeyBmaWxsTWV0YWRhdGFTZWdtZW50IH0gZnJvbSAnbmV4dC9kaXN0L2xpYi9tZXRhZGF0YS9nZXQtbWV0YWRhdGEtcm91dGUnXG5cbiAgZXhwb3J0IGRlZmF1bHQgYXN5bmMgKHByb3BzKSA9PiB7XG4gICAgY29uc3QgaW1hZ2VEYXRhID0ge1widHlwZVwiOlwiaW1hZ2UveC1pY29uXCIsXCJzaXplc1wiOlwiMTZ4MTZcIn1cbiAgICBjb25zdCBpbWFnZVVybCA9IGZpbGxNZXRhZGF0YVNlZ21lbnQoXCIuXCIsIGF3YWl0IHByb3BzLnBhcmFtcywgXCJmYXZpY29uLmljb1wiKVxuXG4gICAgcmV0dXJuIFt7XG4gICAgICAuLi5pbWFnZURhdGEsXG4gICAgICB1cmw6IGltYWdlVXJsICsgXCJcIixcbiAgICB9XVxuICB9Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(ssr)/./app/components/FileUpload.tsx":
/*!***************************************!*\
  !*** ./app/components/FileUpload.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nconst FileUpload = ({ onFileUpload, onFileRemove, uploadedFile, disabled = false })=>{\n    const [uploading, setUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dragOver, setDragOver] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const handleFileSelect = async (file)=>{\n        if (!file) return;\n        // Validate file type\n        const allowedTypes = [\n            \"application/pdf\",\n            \"application/msword\",\n            \"application/vnd.openxmlformats-officedocument.wordprocessingml.document\"\n        ];\n        if (!allowedTypes.includes(file.type)) {\n            setError(\"Invalid file type. Only PDF and Word documents are allowed.\");\n            return;\n        }\n        // Validate file size (10MB limit)\n        const maxSize = 10 * 1024 * 1024; // 10MB\n        if (file.size > maxSize) {\n            setError(\"File size too large. Maximum size is 10MB.\");\n            return;\n        }\n        setError(\"\");\n        setUploading(true);\n        try {\n            const formData = new FormData();\n            formData.append(\"file\", file);\n            const response = await fetch(\"/api/upload\", {\n                method: \"POST\",\n                body: formData\n            });\n            const result = await response.json();\n            if (!response.ok) {\n                throw new Error(result.error || \"Upload failed\");\n            }\n            onFileUpload(result.url, result.filename);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : \"Upload failed\");\n        } finally{\n            setUploading(false);\n        }\n    };\n    const handleFileChange = (e)=>{\n        const file = e.target.files?.[0];\n        if (file) {\n            handleFileSelect(file);\n        }\n    };\n    const handleDrop = (e)=>{\n        e.preventDefault();\n        setDragOver(false);\n        const file = e.dataTransfer.files[0];\n        if (file) {\n            handleFileSelect(file);\n        }\n    };\n    const handleDragOver = (e)=>{\n        e.preventDefault();\n        setDragOver(true);\n    };\n    const handleDragLeave = (e)=>{\n        e.preventDefault();\n        setDragOver(false);\n    };\n    const handleRemoveFile = ()=>{\n        onFileRemove();\n        if (fileInputRef.current) {\n            fileInputRef.current.value = \"\";\n        }\n    };\n    const getFileIcon = (fileName)=>{\n        const extension = fileName.split('.').pop()?.toLowerCase();\n        if (extension === 'pdf') {\n            return '📄';\n        } else if (extension === 'doc' || extension === 'docx') {\n            return '📝';\n        }\n        return '📎';\n    };\n    if (uploadedFile) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"border border-gray-300 rounded-lg p-4 bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-2xl\",\n                                children: getFileIcon(uploadedFile.name)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\FileUpload.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm font-medium text-gray-700\",\n                                        children: uploadedFile.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\FileUpload.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: uploadedFile.url,\n                                        target: \"_blank\",\n                                        rel: \"noopener noreferrer\",\n                                        className: \"text-xs text-blue-600 hover:text-blue-800 underline\",\n                                        children: \"View file\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\FileUpload.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\FileUpload.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\FileUpload.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"button\",\n                        onClick: handleRemoveFile,\n                        disabled: disabled,\n                        className: \"text-red-600 hover:text-red-800 disabled:opacity-50 disabled:cursor-not-allowed\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-5 h-5\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M6 18L18 6M6 6l12 12\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\FileUpload.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\FileUpload.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\FileUpload.tsx\",\n                        lineNumber: 132,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\FileUpload.tsx\",\n                lineNumber: 117,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\FileUpload.tsx\",\n            lineNumber: 116,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-2\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `border-2 border-dashed rounded-lg p-6 text-center transition-colors ${dragOver ? \"border-blue-400 bg-blue-50\" : \"border-gray-300 hover:border-gray-400\"} ${disabled ? \"opacity-50 cursor-not-allowed\" : \"cursor-pointer\"}`,\n                onDrop: handleDrop,\n                onDragOver: handleDragOver,\n                onDragLeave: handleDragLeave,\n                onClick: ()=>!disabled && fileInputRef.current?.click(),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        ref: fileInputRef,\n                        type: \"file\",\n                        accept: \".pdf,.doc,.docx,application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document\",\n                        onChange: handleFileChange,\n                        className: \"hidden\",\n                        disabled: disabled\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\FileUpload.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 9\n                    }, undefined),\n                    uploading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\FileUpload.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600\",\n                                children: \"Uploading...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\FileUpload.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\FileUpload.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 11\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-4xl\",\n                                children: \"\\uD83D\\uDCCE\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\FileUpload.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm font-medium text-gray-700\",\n                                        children: \"Drop your file here or click to browse\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\FileUpload.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-500 mt-1\",\n                                        children: \"PDF or Word documents only (max 10MB)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\FileUpload.tsx\",\n                                        lineNumber: 181,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\FileUpload.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\FileUpload.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\FileUpload.tsx\",\n                lineNumber: 149,\n                columnNumber: 7\n            }, undefined),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-sm text-red-600\",\n                children: error\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\FileUpload.tsx\",\n                lineNumber: 190,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\FileUpload.tsx\",\n        lineNumber: 148,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FileUpload);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/FileUpload.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/NotificationBell.tsx":
/*!*********************************************!*\
  !*** ./app/components/NotificationBell.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NotificationBell: () => (/* binding */ NotificationBell)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_FaBell_react_icons_fa__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=FaBell!=!react-icons/fa */ \"(ssr)/./node_modules/react-icons/fa/index.mjs\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ NotificationBell auto */ \n\n\n\n\nfunction NotificationBell() {\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const [unreadCount, setUnreadCount] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"NotificationBell.useEffect\": ()=>{\n            // Wait for session to be fully loaded and authenticated\n            if (status === \"loading\" || !session?.user?.id) {\n                return;\n            }\n            const fetchUnreadCount = {\n                \"NotificationBell.useEffect.fetchUnreadCount\": async ()=>{\n                    try {\n                        const response = await fetch(\"/api/auth/notifications\", {\n                            headers: {\n                                \"Cache-Control\": \"no-cache\"\n                            }\n                        });\n                        if (response.ok) {\n                            const data = await response.json();\n                            setUnreadCount(data.unreadCount || 0);\n                        } else if (response.status === 401) {\n                            // Unauthorized - session might have expired\n                            setUnreadCount(0);\n                        } else {\n                            // Other errors - keep previous count\n                            console.warn(\"Failed to fetch notification count:\", response.status, response.statusText);\n                        }\n                    } catch (error) {\n                        // Silently handle network errors to avoid console spam\n                        if (error instanceof TypeError && error.message.includes(\"fetch\")) {\n                            // Network error - keep previous count\n                            return;\n                        }\n                        console.error(\"Failed to fetch notification count:\", error);\n                    }\n                }\n            }[\"NotificationBell.useEffect.fetchUnreadCount\"];\n            // Initial fetch with a small delay to ensure session is stable\n            const timeoutId = setTimeout(fetchUnreadCount, 100);\n            // Poll for updates every 30 seconds\n            const interval = setInterval(fetchUnreadCount, 30000);\n            return ({\n                \"NotificationBell.useEffect\": ()=>{\n                    clearTimeout(timeoutId);\n                    clearInterval(interval);\n                }\n            })[\"NotificationBell.useEffect\"];\n        }\n    }[\"NotificationBell.useEffect\"], [\n        session?.user?.id,\n        status\n    ]);\n    // Wait for session to load\n    if (status === \"loading\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-6 h-6 animate-pulse bg-gray-200 rounded\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\NotificationBell.tsx\",\n            lineNumber: 64,\n            columnNumber: 12\n        }, this);\n    }\n    // No session means user is not logged in\n    if (status === \"unauthenticated\" || !session?.user?.id) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n        href: \"/dashboard/notifications\",\n        className: \"relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBell_react_icons_fa__WEBPACK_IMPORTED_MODULE_4__.FaBell, {\n                size: 24,\n                className: \"text-gray-600 hover:text-blue-600 transition-colors\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\NotificationBell.tsx\",\n                lineNumber: 74,\n                columnNumber: 7\n            }, this),\n            unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center animate-pulse\",\n                children: unreadCount > 99 ? \"99+\" : unreadCount\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\NotificationBell.tsx\",\n                lineNumber: 80,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\NotificationBell.tsx\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/NotificationBell.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/NotificationToast.tsx":
/*!**********************************************!*\
  !*** ./app/components/NotificationToast.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotificationToast)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction NotificationToast() {\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const removeNotification = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NotificationToast.useCallback[removeNotification]\": (id)=>{\n            setNotifications({\n                \"NotificationToast.useCallback[removeNotification]\": (prev)=>prev.filter({\n                        \"NotificationToast.useCallback[removeNotification]\": (n)=>n.id !== id\n                    }[\"NotificationToast.useCallback[removeNotification]\"])\n            }[\"NotificationToast.useCallback[removeNotification]\"]);\n        }\n    }[\"NotificationToast.useCallback[removeNotification]\"], []);\n    // Function to add a new toast notification\n    const addNotification = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NotificationToast.useCallback[addNotification]\": (notification)=>{\n            const id = `toast_${Date.now()}_${Math.random().toString(36).substring(7)}`;\n            const newNotification = {\n                ...notification,\n                id\n            };\n            setNotifications({\n                \"NotificationToast.useCallback[addNotification]\": (prev)=>[\n                        ...prev,\n                        newNotification\n                    ]\n            }[\"NotificationToast.useCallback[addNotification]\"]);\n            // Auto-remove after 5 seconds\n            setTimeout({\n                \"NotificationToast.useCallback[addNotification]\": ()=>{\n                    removeNotification(id);\n                }\n            }[\"NotificationToast.useCallback[addNotification]\"], 5000);\n        }\n    }[\"NotificationToast.useCallback[addNotification]\"], [\n        removeNotification\n    ]);\n    // Poll for new notifications (in a real app, you'd use WebSockets or Server-Sent Events)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NotificationToast.useEffect\": ()=>{\n            // Wait for session to be fully loaded and authenticated\n            if (status === \"loading\" || !session?.user?.id) return;\n            const checkForNewNotifications = {\n                \"NotificationToast.useEffect.checkForNewNotifications\": async ()=>{\n                    try {\n                        const response = await fetch(\"/api/auth/notifications\", {\n                            headers: {\n                                \"Cache-Control\": \"no-cache\"\n                            }\n                        });\n                        if (response.ok) {\n                            await response.json();\n                        // This is a simple implementation - in production you'd want to track which notifications are new\n                        // and only show toasts for truly new ones\n                        } else if (response.status === 401) {\n                            // Unauthorized - session might have expired, stop polling\n                            return;\n                        } else {\n                            console.warn(\"Failed to fetch notifications:\", response.status, response.statusText);\n                        }\n                    } catch (error) {\n                        // Silently handle network errors to avoid spamming console\n                        // Only log if it's not a network connectivity issue\n                        if (error instanceof TypeError && error.message.includes(\"fetch\")) {\n                            // Network error - don't log to avoid spam\n                            return;\n                        }\n                        console.error(\"Failed to check for notifications:\", error);\n                    }\n                }\n            }[\"NotificationToast.useEffect.checkForNewNotifications\"];\n            // Initial delay to ensure session is stable, then check every 30 seconds\n            const timeoutId = setTimeout({\n                \"NotificationToast.useEffect.timeoutId\": ()=>{\n                    checkForNewNotifications(); // Initial check\n                }\n            }[\"NotificationToast.useEffect.timeoutId\"], 1000);\n            const interval = setInterval(checkForNewNotifications, 30000);\n            return ({\n                \"NotificationToast.useEffect\": ()=>{\n                    clearTimeout(timeoutId);\n                    clearInterval(interval);\n                }\n            })[\"NotificationToast.useEffect\"];\n        }\n    }[\"NotificationToast.useEffect\"], [\n        session?.user?.id,\n        status\n    ]);\n    // Expose the addNotification function globally for other components to use\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NotificationToast.useEffect\": ()=>{\n            window.showNotificationToast = addNotification;\n            return ({\n                \"NotificationToast.useEffect\": ()=>{\n                    delete window.showNotificationToast;\n                }\n            })[\"NotificationToast.useEffect\"];\n        }\n    }[\"NotificationToast.useEffect\"], [\n        addNotification\n    ]);\n    const getToastStyles = (type)=>{\n        const baseStyles = \"p-4 rounded-lg shadow-lg border-l-4 max-w-sm\";\n        switch(type){\n            case \"success\":\n                return `${baseStyles} bg-green-50 border-green-400 text-green-800`;\n            case \"error\":\n                return `${baseStyles} bg-red-50 border-red-400 text-red-800`;\n            case \"warning\":\n                return `${baseStyles} bg-yellow-50 border-yellow-400 text-yellow-800`;\n            case \"info\":\n            default:\n                return `${baseStyles} bg-blue-50 border-blue-400 text-blue-800`;\n        }\n    };\n    const getIcon = (type)=>{\n        switch(type){\n            case \"success\":\n                return \"✅\";\n            case \"error\":\n                return \"❌\";\n            case \"warning\":\n                return \"⚠️\";\n            case \"info\":\n            default:\n                return \"ℹ️\";\n        }\n    };\n    if (notifications.length === 0) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed top-4 right-4 z-50 space-y-2\",\n        children: notifications.map((notification)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `${getToastStyles(notification.type)} animate-slide-in-right`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-lg mr-3\",\n                            children: getIcon(notification.type)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\NotificationToast.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-medium\",\n                                    children: notification.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\NotificationToast.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm mt-1\",\n                                    children: notification.message\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\NotificationToast.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\NotificationToast.tsx\",\n                            lineNumber: 142,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>removeNotification(notification.id),\n                            className: \"ml-3 text-gray-400 hover:text-gray-600\",\n                            children: \"\\xd7\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\NotificationToast.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\NotificationToast.tsx\",\n                    lineNumber: 140,\n                    columnNumber: 11\n                }, this)\n            }, notification.id, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\NotificationToast.tsx\",\n                lineNumber: 136,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\NotificationToast.tsx\",\n        lineNumber: 134,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/NotificationToast.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/RichTextEditor.module.css":
/*!**************************************************!*\
  !*** ./app/components/RichTextEditor.module.css ***!
  \**************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"editor\": \"RichTextEditor_editor__f90n2\",\n\t\"toolbar\": \"RichTextEditor_toolbar__072Cb\",\n\t\"toolbarGroup\": \"RichTextEditor_toolbarGroup__Pkt5f\",\n\t\"toolbarButton\": \"RichTextEditor_toolbarButton__frh0r\",\n\t\"active\": \"RichTextEditor_active__ir8Mh\",\n\t\"editorContent\": \"RichTextEditor_editorContent__A1QHg\",\n\t\"ProseMirror\": \"RichTextEditor_ProseMirror___JNXh\",\n\t\"is-editor-empty\": \"RichTextEditor_is-editor-empty__OyISz\",\n\t\"characterCount\": \"RichTextEditor_characterCount__c9x5l\"\n};\n\nmodule.exports.__checksum = \"897f44dc4b76\"\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvY29tcG9uZW50cy9SaWNoVGV4dEVkaXRvci5tb2R1bGUuY3NzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSx5QkFBeUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYmljaGVcXFRpbmEgRWR1Y2F0aW9uLm9yZ1xcUmV2aWV3IFJlcXVlc3QgYmFja2VuZFxcQVJSUy1OZXh0SlNcXHRpbmEtZWR1Y2F0aW9uXFxhcHBcXGNvbXBvbmVudHNcXFJpY2hUZXh0RWRpdG9yLm1vZHVsZS5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gRXhwb3J0c1xubW9kdWxlLmV4cG9ydHMgPSB7XG5cdFwiZWRpdG9yXCI6IFwiUmljaFRleHRFZGl0b3JfZWRpdG9yX19mOTBuMlwiLFxuXHRcInRvb2xiYXJcIjogXCJSaWNoVGV4dEVkaXRvcl90b29sYmFyX18wNzJDYlwiLFxuXHRcInRvb2xiYXJHcm91cFwiOiBcIlJpY2hUZXh0RWRpdG9yX3Rvb2xiYXJHcm91cF9fUGt0NWZcIixcblx0XCJ0b29sYmFyQnV0dG9uXCI6IFwiUmljaFRleHRFZGl0b3JfdG9vbGJhckJ1dHRvbl9fZnJoMHJcIixcblx0XCJhY3RpdmVcIjogXCJSaWNoVGV4dEVkaXRvcl9hY3RpdmVfX2lyOE1oXCIsXG5cdFwiZWRpdG9yQ29udGVudFwiOiBcIlJpY2hUZXh0RWRpdG9yX2VkaXRvckNvbnRlbnRfX0ExUUhnXCIsXG5cdFwiUHJvc2VNaXJyb3JcIjogXCJSaWNoVGV4dEVkaXRvcl9Qcm9zZU1pcnJvcl9fX0pOWGhcIixcblx0XCJpcy1lZGl0b3ItZW1wdHlcIjogXCJSaWNoVGV4dEVkaXRvcl9pcy1lZGl0b3ItZW1wdHlfX095SVN6XCIsXG5cdFwiY2hhcmFjdGVyQ291bnRcIjogXCJSaWNoVGV4dEVkaXRvcl9jaGFyYWN0ZXJDb3VudF9fYzl4NWxcIlxufTtcblxubW9kdWxlLmV4cG9ydHMuX19jaGVja3N1bSA9IFwiODk3ZjQ0ZGM0Yjc2XCJcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./app/components/RichTextEditor.module.css\n");

/***/ }),

/***/ "(ssr)/./app/components/RichTextEditor.tsx":
/*!*******************************************!*\
  !*** ./app/components/RichTextEditor.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _tiptap_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @tiptap/react */ \"(ssr)/./node_modules/@tiptap/react/dist/index.js\");\n/* harmony import */ var _tiptap_starter_kit__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tiptap/starter-kit */ \"(ssr)/./node_modules/@tiptap/starter-kit/dist/index.js\");\n/* harmony import */ var _tiptap_extension_placeholder__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tiptap/extension-placeholder */ \"(ssr)/./node_modules/@tiptap/extension-placeholder/dist/index.js\");\n/* harmony import */ var _tiptap_extension_character_count__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tiptap/extension-character-count */ \"(ssr)/./node_modules/@tiptap/extension-character-count/dist/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _RichTextEditor_module_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./RichTextEditor.module.css */ \"(ssr)/./app/components/RichTextEditor.module.css\");\n/* harmony import */ var _RichTextEditor_module_css__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_RichTextEditor_module_css__WEBPACK_IMPORTED_MODULE_5__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nconst RichTextEditor = ({ content, onChange, placeholder = \"Enter manuscript content...\", className = \"\" })=>{\n    const editor = (0,_tiptap_react__WEBPACK_IMPORTED_MODULE_6__.useEditor)({\n        extensions: [\n            _tiptap_starter_kit__WEBPACK_IMPORTED_MODULE_1__[\"default\"].configure({\n                heading: {\n                    levels: [\n                        1,\n                        2,\n                        3,\n                        4\n                    ]\n                }\n            }),\n            _tiptap_extension_placeholder__WEBPACK_IMPORTED_MODULE_2__[\"default\"].configure({\n                placeholder\n            }),\n            _tiptap_extension_character_count__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n        ],\n        content,\n        onUpdate: {\n            \"RichTextEditor.useEditor[editor]\": ({ editor })=>{\n                onChange(editor.getHTML());\n            }\n        }[\"RichTextEditor.useEditor[editor]\"],\n        editorProps: {\n            attributes: {\n                class: \"focus:outline-none\"\n            }\n        }\n    });\n    if (!editor) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `${(_RichTextEditor_module_css__WEBPACK_IMPORTED_MODULE_5___default().editor)} ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_RichTextEditor_module_css__WEBPACK_IMPORTED_MODULE_5___default().toolbar),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_RichTextEditor_module_css__WEBPACK_IMPORTED_MODULE_5___default().toolbarGroup),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>editor.chain().focus().toggleBold().run(),\n                                className: `${(_RichTextEditor_module_css__WEBPACK_IMPORTED_MODULE_5___default().toolbarButton)} ${editor.isActive(\"bold\") ? (_RichTextEditor_module_css__WEBPACK_IMPORTED_MODULE_5___default().active) : \"\"}`,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: \"B\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\RichTextEditor.tsx\",\n                                    lineNumber: 62,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\RichTextEditor.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>editor.chain().focus().toggleItalic().run(),\n                                className: `${(_RichTextEditor_module_css__WEBPACK_IMPORTED_MODULE_5___default().toolbarButton)} ${editor.isActive(\"italic\") ? (_RichTextEditor_module_css__WEBPACK_IMPORTED_MODULE_5___default().active) : \"\"}`,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                                    children: \"I\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\RichTextEditor.tsx\",\n                                    lineNumber: 71,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\RichTextEditor.tsx\",\n                                lineNumber: 64,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>editor.chain().focus().toggleStrike().run(),\n                                className: `${(_RichTextEditor_module_css__WEBPACK_IMPORTED_MODULE_5___default().toolbarButton)} ${editor.isActive(\"strike\") ? (_RichTextEditor_module_css__WEBPACK_IMPORTED_MODULE_5___default().active) : \"\"}`,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"s\", {\n                                    children: \"S\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\RichTextEditor.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\RichTextEditor.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\RichTextEditor.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_RichTextEditor_module_css__WEBPACK_IMPORTED_MODULE_5___default().toolbarGroup),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>editor.chain().focus().toggleHeading({\n                                        level: 1\n                                    }).run(),\n                                className: `${(_RichTextEditor_module_css__WEBPACK_IMPORTED_MODULE_5___default().toolbarButton)} ${editor.isActive(\"heading\", {\n                                    level: 1\n                                }) ? (_RichTextEditor_module_css__WEBPACK_IMPORTED_MODULE_5___default().active) : \"\"}`,\n                                children: \"H1\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\RichTextEditor.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>editor.chain().focus().toggleHeading({\n                                        level: 2\n                                    }).run(),\n                                className: `${(_RichTextEditor_module_css__WEBPACK_IMPORTED_MODULE_5___default().toolbarButton)} ${editor.isActive(\"heading\", {\n                                    level: 2\n                                }) ? (_RichTextEditor_module_css__WEBPACK_IMPORTED_MODULE_5___default().active) : \"\"}`,\n                                children: \"H2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\RichTextEditor.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>editor.chain().focus().toggleHeading({\n                                        level: 3\n                                    }).run(),\n                                className: `${(_RichTextEditor_module_css__WEBPACK_IMPORTED_MODULE_5___default().toolbarButton)} ${editor.isActive(\"heading\", {\n                                    level: 3\n                                }) ? (_RichTextEditor_module_css__WEBPACK_IMPORTED_MODULE_5___default().active) : \"\"}`,\n                                children: \"H3\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\RichTextEditor.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\RichTextEditor.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_RichTextEditor_module_css__WEBPACK_IMPORTED_MODULE_5___default().toolbarGroup),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>editor.chain().focus().toggleBulletList().run(),\n                                className: `${(_RichTextEditor_module_css__WEBPACK_IMPORTED_MODULE_5___default().toolbarButton)} ${editor.isActive(\"bulletList\") ? (_RichTextEditor_module_css__WEBPACK_IMPORTED_MODULE_5___default().active) : \"\"}`,\n                                children: \"• List\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\RichTextEditor.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>editor.chain().focus().toggleOrderedList().run(),\n                                className: `${(_RichTextEditor_module_css__WEBPACK_IMPORTED_MODULE_5___default().toolbarButton)} ${editor.isActive(\"orderedList\") ? (_RichTextEditor_module_css__WEBPACK_IMPORTED_MODULE_5___default().active) : \"\"}`,\n                                children: \"1. List\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\RichTextEditor.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\RichTextEditor.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_RichTextEditor_module_css__WEBPACK_IMPORTED_MODULE_5___default().toolbarGroup),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>editor.chain().focus().toggleBlockquote().run(),\n                                className: `${(_RichTextEditor_module_css__WEBPACK_IMPORTED_MODULE_5___default().toolbarButton)} ${editor.isActive(\"blockquote\") ? (_RichTextEditor_module_css__WEBPACK_IMPORTED_MODULE_5___default().active) : \"\"}`,\n                                children: \"Quote\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\RichTextEditor.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>editor.chain().focus().toggleCode().run(),\n                                className: `${(_RichTextEditor_module_css__WEBPACK_IMPORTED_MODULE_5___default().toolbarButton)} ${editor.isActive(\"code\") ? (_RichTextEditor_module_css__WEBPACK_IMPORTED_MODULE_5___default().active) : \"\"}`,\n                                children: \"Code\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\RichTextEditor.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\RichTextEditor.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_RichTextEditor_module_css__WEBPACK_IMPORTED_MODULE_5___default().toolbarGroup),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>editor.chain().focus().setHorizontalRule().run(),\n                                className: (_RichTextEditor_module_css__WEBPACK_IMPORTED_MODULE_5___default().toolbarButton),\n                                children: \"HR\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\RichTextEditor.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>editor.chain().focus().undo().run(),\n                                disabled: !editor.can().undo(),\n                                className: (_RichTextEditor_module_css__WEBPACK_IMPORTED_MODULE_5___default().toolbarButton),\n                                children: \"Undo\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\RichTextEditor.tsx\",\n                                lineNumber: 168,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>editor.chain().focus().redo().run(),\n                                disabled: !editor.can().redo(),\n                                className: (_RichTextEditor_module_css__WEBPACK_IMPORTED_MODULE_5___default().toolbarButton),\n                                children: \"Redo\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\RichTextEditor.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\RichTextEditor.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\RichTextEditor.tsx\",\n                lineNumber: 52,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_RichTextEditor_module_css__WEBPACK_IMPORTED_MODULE_5___default().editorContent),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tiptap_react__WEBPACK_IMPORTED_MODULE_6__.EditorContent, {\n                    editor: editor\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\RichTextEditor.tsx\",\n                    lineNumber: 189,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\RichTextEditor.tsx\",\n                lineNumber: 188,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_RichTextEditor_module_css__WEBPACK_IMPORTED_MODULE_5___default().characterCount),\n                children: [\n                    editor.storage.characterCount.characters(),\n                    \" characters\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\RichTextEditor.tsx\",\n                lineNumber: 193,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\RichTextEditor.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (RichTextEditor);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/RichTextEditor.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/authSect.tsx":
/*!*************************************!*\
  !*** ./app/components/authSect.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AuthSect)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _NotificationBell__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./NotificationBell */ \"(ssr)/./app/components/NotificationBell.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction AuthSect() {\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_1__.useSession)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const getCurrentUrl = ()=>{\n        const params = searchParams.toString();\n        return params ? `${pathname}?${params}` : pathname;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex justify-center items-center gap-5 relative text-gray-400\",\n        children: [\n            !session && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    className: \"px-5 py-2 border border-black rounded text-gray-800 hover:bg-black hover:text-white hover:bg-opacity-10 transition-colors\",\n                    onClick: ()=>(0,next_auth_react__WEBPACK_IMPORTED_MODULE_1__.signIn)(undefined, {\n                            callbackUrl: getCurrentUrl()\n                        }),\n                    children: \"Sign In\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\authSect.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false),\n            session && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NotificationBell__WEBPACK_IMPORTED_MODULE_3__.NotificationBell, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\authSect.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-gray-800 font-medium\",\n                        children: session.user?.name\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\authSect.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"px-5 py-2 border border-white rounded bg-blue-900 text-white hover:bg-black\",\n                        onClick: ()=>(0,next_auth_react__WEBPACK_IMPORTED_MODULE_1__.signOut)({\n                                callbackUrl: \"/\"\n                            }),\n                        children: \"Sign out\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\authSect.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\authSect.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/authSect.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/footer.tsx":
/*!***********************************!*\
  !*** ./app/components/footer.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction Footer() {\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('');\n    const handleEmailSubmit = (e)=>{\n        // setEmail: e.target.value\n        return undefined;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-gray-900 text-white py-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto w-[90%] max-w-7xl\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-bold mb-4\",\n                                        children: \"Quick Links\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                                        lineNumber: 19,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: \"/\",\n                                                    className: \"text-gray-300 hover:text-white\",\n                                                    children: \"Home\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                                                    lineNumber: 22,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                                                lineNumber: 21,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: \"#\",\n                                                    className: \"text-gray-300 hover:text-white\",\n                                                    children: \"About\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                                                    lineNumber: 27,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                                                lineNumber: 26,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: \"/books\",\n                                                    className: \"text-gray-300 hover:text-white\",\n                                                    children: \"Books\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                                                    lineNumber: 32,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                                                lineNumber: 31,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: \"/journals\",\n                                                    className: \"text-gray-300 hover:text-white\",\n                                                    children: \"Journals\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                                                    lineNumber: 40,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                                                lineNumber: 39,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: \"/articles\",\n                                                    className: \"text-gray-300 hover:text-white\",\n                                                    children: \"Articles\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                                                    lineNumber: 48,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                                                lineNumber: 47,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: \"#\",\n                                                    className: \"text-gray-300 hover:text-white\",\n                                                    children: \"Privacy Policy\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                                                    lineNumber: 56,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                                                lineNumber: 55,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                                        lineNumber: 20,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                                lineNumber: 18,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                            lineNumber: 17,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-bold mb-4\",\n                                    children: \"Services\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                                    lineNumber: 65,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"#\",\n                                                className: \"text-gray-300 hover:text-white\",\n                                                children: \"Publishing\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                                                lineNumber: 68,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                                            lineNumber: 67,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"#\",\n                                                className: \"text-gray-300 hover:text-white\",\n                                                children: \"Editing\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                                                lineNumber: 73,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                                            lineNumber: 72,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"#\",\n                                                className: \"text-gray-300 hover:text-white\",\n                                                children: \"Formatting\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                                                lineNumber: 78,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                                            lineNumber: 77,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-bold mb-4\",\n                                    children: \"Newsletter Sign Up\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                                    lineNumber: 85,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"email\",\n                                            placeholder: \"Enter your Email\",\n                                            className: \"bg-gray-600 px-4 py-2 w-full rounded\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                                            lineNumber: 87,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"bg-white text-gray-900 ms-2 px-4 py-2 rounded\",\n                                            children: \"Submit\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                                            lineNumber: 92,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                    lineNumber: 16,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-8 pt-8 border-t border-gray-700 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-400\",\n                        children: \"Copyright \\xa9 2025 - Tina Education\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                    lineNumber: 98,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n            lineNumber: 15,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n        lineNumber: 14,\n        columnNumber: 7\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/footer.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/home_nav.tsx":
/*!*************************************!*\
  !*** ./app/components/home_nav.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomeNav)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _authSect__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./authSect */ \"(ssr)/./app/components/authSect.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_FaBars_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=FaBars,FaTimes!=!react-icons/fa */ \"(ssr)/./node_modules/react-icons/fa/index.mjs\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction HomeNav() {\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_4__.useSession)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.usePathname)();\n    const toggleMenu = ()=>{\n        setIsMenuOpen(!isMenuOpen);\n    };\n    // Check if user is signed in and on home page\n    const isSignedInOnHomePage = session && pathname === \"/\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"fixed top-0 w-full bg-white shadow-md z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto w-[90%] max-w-7xl\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center py-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                        href: \"/\",\n                        className: \"text-2xl font-bold text-gray-800\",\n                        children: \"Tina Education\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home_nav.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"text-gray-800 text-2xl md:hidden\",\n                        onClick: toggleMenu,\n                        \"aria-label\": \"Toggle Menu\",\n                        children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBars_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_6__.FaTimes, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home_nav.tsx\",\n                            lineNumber: 33,\n                            columnNumber: 27\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBars_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_6__.FaBars, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home_nav.tsx\",\n                            lineNumber: 33,\n                            columnNumber: 41\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home_nav.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: `${isMenuOpen ? \"block\" : \"hidden\"} md:block absolute md:static top-full left-0 w-full md:w-auto bg-white md:bg-transparent shadow-md md:shadow-none`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            className: \"flex flex-col md:flex-row items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: \"ml-0 md:ml-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: isSignedInOnHomePage ? \"/dashboard\" : \"/\",\n                                        className: \"text-gray-800 font-medium\",\n                                        children: isSignedInOnHomePage ? \"Dashboard\" : \"Home\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home_nav.tsx\",\n                                        lineNumber: 42,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home_nav.tsx\",\n                                    lineNumber: 41,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: \"ml-0 md:ml-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/books\",\n                                        className: \"text-gray-800 font-medium\",\n                                        children: \"Books\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home_nav.tsx\",\n                                        lineNumber: 51,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home_nav.tsx\",\n                                    lineNumber: 50,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: \"ml-0 md:ml-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/journals\",\n                                        className: \"text-gray-800 font-medium\",\n                                        children: \"Journals\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home_nav.tsx\",\n                                        lineNumber: 56,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home_nav.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 15\n                                }, this),\n                                session && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: \"ml-0 md:ml-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/wishlist\",\n                                        className: \"text-gray-800 font-medium\",\n                                        children: \"Wishlist\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home_nav.tsx\",\n                                        lineNumber: 62,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home_nav.tsx\",\n                                    lineNumber: 61,\n                                    columnNumber: 17\n                                }, this),\n                                session && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: \"ml-0 md:ml-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/cart\",\n                                        className: \"text-gray-800 font-medium\",\n                                        children: \"Cart\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home_nav.tsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home_nav.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: \"ml-0 md:ml-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"#\",\n                                        className: \"text-gray-800 font-medium\",\n                                        children: \"Publisher with Us\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home_nav.tsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home_nav.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: \"ml-0 md:ml-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-center items-center gap-5 mb-8 md:mb-0 relative text-gray-500\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_authSect__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home_nav.tsx\",\n                                            lineNumber: 81,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home_nav.tsx\",\n                                        lineNumber: 80,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home_nav.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home_nav.tsx\",\n                            lineNumber: 40,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home_nav.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home_nav.tsx\",\n                lineNumber: 24,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home_nav.tsx\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home_nav.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/home_nav.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/submit.tsx":
/*!***********************************!*\
  !*** ./app/components/submit.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Submit)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _RichTextEditor__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./RichTextEditor */ \"(ssr)/./app/components/RichTextEditor.tsx\");\n/* harmony import */ var _FileUpload__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./FileUpload */ \"(ssr)/./app/components/FileUpload.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction Submit() {\n    // State for form fields\n    const [title, setTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [abstract, setAbstract] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [keywords, setKeywords] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [uploadedFile, setUploadedFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [submitting, setSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Check if user is authenticated\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    if (!session) {\n        // Handle unauthenticated state\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-40\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-red-600 mb-8\",\n                children: \"You must be logged in to submit a manuscript.\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\submit.tsx\",\n                lineNumber: 28,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\submit.tsx\",\n            lineNumber: 27,\n            columnNumber: 7\n        }, this);\n    }\n    // Submit handler\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setSubmitting(true);\n        setError(\"\");\n        setSuccess(\"\");\n        try {\n            const res = await fetch(\"/api/manuscripts\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    title,\n                    abstract,\n                    content,\n                    keywords,\n                    uploadedFile: uploadedFile?.url,\n                    uploadedFileName: uploadedFile?.name\n                })\n            });\n            if (!res.ok) throw new Error(\"Submission failed\");\n            setSuccess(\"Manuscript submitted successfully!\");\n            setTitle(\"\");\n            setAbstract(\"\");\n            setContent(\"\");\n            setKeywords(\"\");\n            setUploadedFile(null);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : \"Submission error\");\n        } finally{\n            setSubmitting(false);\n        }\n    };\n    const form = {\n        hidden_tag: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                type: \"hidden\",\n                name: \"csrf_token\",\n                value: \"dummy_token\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\submit.tsx\",\n                lineNumber: 70,\n                columnNumber: 7\n            }, this),\n        title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n            type: \"text\",\n            id: \"title\",\n            className: \"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-gray-700 sm:text-sm placeholder-gray-500\",\n            placeholder: \"Enter manuscript title\",\n            value: title,\n            onChange: (e)=>setTitle(e.target.value),\n            required: true\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\submit.tsx\",\n            lineNumber: 73,\n            columnNumber: 7\n        }, this),\n        abstract: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                    htmlFor: \"abstract\",\n                    className: \"block text-gray-700 text-sm font-bold mb-2\",\n                    children: \"Abstract\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\submit.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                    id: \"abstract\",\n                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-gray-700 sm:text-sm placeholder-gray-500\",\n                    placeholder: \"Enter Abstract\",\n                    value: abstract,\n                    onChange: (e)=>setAbstract(e.target.value),\n                    required: true\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\submit.tsx\",\n                    lineNumber: 91,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true),\n        content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                    htmlFor: \"content\",\n                    className: \"block text-gray-700 text-sm font-bold mb-2\",\n                    children: \"Manuscript Content\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\submit.tsx\",\n                    lineNumber: 103,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_RichTextEditor__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    content: content,\n                    onChange: setContent,\n                    placeholder: \"Enter your manuscript content here. Use the toolbar above to format your text with headings, lists, quotes, and more...\",\n                    className: \"w-full\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\submit.tsx\",\n                    lineNumber: 109,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true),\n        keywords: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                    htmlFor: \"keywords\",\n                    className: \"block text-gray-700 text-sm font-bold mb-2\",\n                    children: \"Keywords\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\submit.tsx\",\n                    lineNumber: 119,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                    type: \"text\",\n                    id: \"keywords\",\n                    className: \"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-gray-700 sm:text-sm placeholder-gray-500\",\n                    placeholder: \"Enter keywords (comma separated)\",\n                    value: keywords,\n                    onChange: (e)=>setKeywords(e.target.value),\n                    required: true\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\submit.tsx\",\n                    lineNumber: 125,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true),\n        submit: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                type: \"submit\",\n                className: \"bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline\",\n                disabled: submitting,\n                children: submitting ? \"Submitting...\" : \"Submit Manuscript\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\submit.tsx\",\n                lineNumber: 137,\n                columnNumber: 7\n            }, this)\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-gray-100 min-h-screen py-6 mt-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto max-w-3xl bg-white shadow-md rounded-lg px-8 pt-6 pb-8 mt-8 mb-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                    href: \"/dashboard\",\n                    className: \"text-indigo-600 hover:text-indigo-800 mb-4 inline-block\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"i\", {\n                            className: \"mr-2\",\n                            children: \"<=\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\submit.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 11\n                        }, this),\n                        \"Back to Dashboard\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\submit.tsx\",\n                    lineNumber: 150,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    className: \"mb-4\",\n                    id: \"manuscriptForm\",\n                    method: \"POST\",\n                    onSubmit: handleSubmit,\n                    children: [\n                        form.hidden_tag(),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-center text-2xl font-bold text-gray-800 mb-6\",\n                            children: \"Manuscript Submission\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\submit.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"title\",\n                                    className: \"block text-gray-700 text-sm font-bold mb-2\",\n                                    children: \"Title\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\submit.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 13\n                                }, this),\n                                form.title\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\submit.tsx\",\n                            lineNumber: 168,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4\",\n                            children: form.abstract\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\submit.tsx\",\n                            lineNumber: 178,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4\",\n                            children: form.content\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\submit.tsx\",\n                            lineNumber: 180,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4\",\n                            children: form.keywords\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\submit.tsx\",\n                            lineNumber: 182,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-gray-700 text-sm font-bold mb-2\",\n                                    children: \"Upload Manuscript File (Optional)\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\submit.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600 mb-3\",\n                                    children: \"You can upload a PDF or Word document instead of or in addition to typing your content above.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\submit.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FileUpload__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    onFileUpload: (url, name)=>setUploadedFile({\n                                            url,\n                                            name\n                                        }),\n                                    onFileRemove: ()=>setUploadedFile(null),\n                                    uploadedFile: uploadedFile,\n                                    disabled: submitting\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\submit.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\submit.tsx\",\n                            lineNumber: 184,\n                            columnNumber: 11\n                        }, this),\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-red-600 mb-2\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\submit.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 21\n                        }, this),\n                        success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-green-600 mb-2\",\n                            children: success\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\submit.tsx\",\n                            lineNumber: 201,\n                            columnNumber: 23\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-end\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    href: \"/guidelines\",\n                                    className: \"bg-transparent hover:bg-gray-300 text-gray-700 font-semibold py-2 px-4 border border-gray-300 rounded shadow-sm hover:border-transparent mr-2\",\n                                    children: \"Submission Guidelines\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\submit.tsx\",\n                                    lineNumber: 204,\n                                    columnNumber: 13\n                                }, this),\n                                form.submit()\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\submit.tsx\",\n                            lineNumber: 203,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\submit.tsx\",\n                    lineNumber: 157,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\submit.tsx\",\n            lineNumber: 149,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\submit.tsx\",\n        lineNumber: 148,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/submit.tsx\n");

/***/ }),

/***/ "(ssr)/./app/context/AuthProvider.tsx":
/*!**************************************!*\
  !*** ./app/context/AuthProvider.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AuthProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction AuthProvider({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_1__.SessionProvider, {\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\context\\\\AuthProvider.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvY29udGV4dC9BdXRoUHJvdmlkZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBRWtEO0FBRW5DLFNBQVNDLGFBQWEsRUFBRUMsUUFBUSxFQUFpQztJQUM5RSxxQkFDRSw4REFBQ0YsNERBQWVBO2tCQUNiRTs7Ozs7O0FBR1AiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYmljaGVcXFRpbmEgRWR1Y2F0aW9uLm9yZ1xcUmV2aWV3IFJlcXVlc3QgYmFja2VuZFxcQVJSUy1OZXh0SlNcXHRpbmEtZWR1Y2F0aW9uXFxhcHBcXGNvbnRleHRcXEF1dGhQcm92aWRlci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xyXG5cclxuaW1wb3J0IHsgU2Vzc2lvblByb3ZpZGVyIH0gZnJvbSAnbmV4dC1hdXRoL3JlYWN0JztcclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEF1dGhQcm92aWRlcih7IGNoaWxkcmVuIH06IHsgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZSB9KSB7XHJcbiAgcmV0dXJuIChcclxuICAgIDxTZXNzaW9uUHJvdmlkZXI+XHJcbiAgICAgIHtjaGlsZHJlbn1cclxuICAgIDwvU2Vzc2lvblByb3ZpZGVyPlxyXG4gICk7XHJcbn0iXSwibmFtZXMiOlsiU2Vzc2lvblByb3ZpZGVyIiwiQXV0aFByb3ZpZGVyIiwiY2hpbGRyZW4iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./app/context/AuthProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccomponents%5C%5Cfooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccomponents%5C%5Chome_nav.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccomponents%5C%5CNotificationToast.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccontext%5C%5CAuthProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccomponents%5C%5Cfooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccomponents%5C%5Chome_nav.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccomponents%5C%5CNotificationToast.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccontext%5C%5CAuthProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/components/footer.tsx */ \"(ssr)/./app/components/footer.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/components/home_nav.tsx */ \"(ssr)/./app/components/home_nav.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/components/NotificationToast.tsx */ \"(ssr)/./app/components/NotificationToast.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/context/AuthProvider.tsx */ \"(ssr)/./app/context/AuthProvider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccomponents%5C%5Cfooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccomponents%5C%5Chome_nav.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccomponents%5C%5CNotificationToast.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccontext%5C%5CAuthProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccomponents%5C%5Csubmit.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext-auth%5C%5Creact.js%22%2C%22ids%22%3A%5B%22SessionProvider%22%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccomponents%5C%5Csubmit.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext-auth%5C%5Creact.js%22%2C%22ids%22%3A%5B%22SessionProvider%22%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/components/submit.tsx */ \"(ssr)/./app/components/submit.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-auth/react.js */ \"(ssr)/./node_modules/next-auth/react.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2JpY2hlJTVDJTVDVGluYSUyMEVkdWNhdGlvbi5vcmclNUMlNUNSZXZpZXclMjBSZXF1ZXN0JTIwYmFja2VuZCU1QyU1Q0FSUlMtTmV4dEpTJTVDJTVDdGluYS1lZHVjYXRpb24lNUMlNUNhcHAlNUMlNUNjb21wb25lbnRzJTVDJTVDc3VibWl0LnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDYmljaGUlNUMlNUNUaW5hJTIwRWR1Y2F0aW9uLm9yZyU1QyU1Q1JldmlldyUyMFJlcXVlc3QlMjBiYWNrZW5kJTVDJTVDQVJSUy1OZXh0SlMlNUMlNUN0aW5hLWVkdWNhdGlvbiU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQtYXV0aCU1QyU1Q3JlYWN0LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyU2Vzc2lvblByb3ZpZGVyJTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxrS0FBd0w7QUFDeEw7QUFDQSw4S0FBc00iLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcImRlZmF1bHRcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxiaWNoZVxcXFxUaW5hIEVkdWNhdGlvbi5vcmdcXFxcUmV2aWV3IFJlcXVlc3QgYmFja2VuZFxcXFxBUlJTLU5leHRKU1xcXFx0aW5hLWVkdWNhdGlvblxcXFxhcHBcXFxcY29tcG9uZW50c1xcXFxzdWJtaXQudHN4XCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJTZXNzaW9uUHJvdmlkZXJcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxiaWNoZVxcXFxUaW5hIEVkdWNhdGlvbi5vcmdcXFxcUmV2aWV3IFJlcXVlc3QgYmFja2VuZFxcXFxBUlJTLU5leHRKU1xcXFx0aW5hLWVkdWNhdGlvblxcXFxub2RlX21vZHVsZXNcXFxcbmV4dC1hdXRoXFxcXHJlYWN0LmpzXCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccomponents%5C%5Csubmit.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext-auth%5C%5Creact.js%22%2C%22ids%22%3A%5B%22SessionProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "../../server/app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@auth","vendor-chunks/next-auth","vendor-chunks/@swc","vendor-chunks/react-icons","vendor-chunks/@popperjs","vendor-chunks/@tiptap","vendor-chunks/w3c-keyname","vendor-chunks/rope-sequence","vendor-chunks/prosemirror-view","vendor-chunks/prosemirror-transform","vendor-chunks/prosemirror-state","vendor-chunks/prosemirror-schema-list","vendor-chunks/prosemirror-model","vendor-chunks/prosemirror-keymap","vendor-chunks/prosemirror-history","vendor-chunks/prosemirror-gapcursor","vendor-chunks/prosemirror-dropcursor","vendor-chunks/prosemirror-commands","vendor-chunks/orderedmap","vendor-chunks/tippy.js"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fmanuscripts%2Fnew%2Fpage&page=%2Fmanuscripts%2Fnew%2Fpage&appPaths=%2Fmanuscripts%2Fnew%2Fpage&pagePath=private-next-app-dir%2Fmanuscripts%2Fnew%2Fpage.tsx&appDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();