/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/dashboard/page";
exports.ids = ["app/dashboard/page"];
exports.modules = {

/***/ "(rsc)/./app/components/NotificationToast.tsx":
/*!**********************************************!*\
  !*** ./app/components/NotificationToast.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\NotificationToast.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\NotificationToast.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./app/components/dashboard/DashboardStats.tsx":
/*!*****************************************************!*\
  !*** ./app/components/dashboard/DashboardStats.tsx ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardStats)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/auth */ \"(rsc)/./auth.ts\");\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../prisma */ \"(rsc)/./prisma.ts\");\n/* harmony import */ var _StatsCard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./StatsCard */ \"(rsc)/./app/components/dashboard/StatsCard.tsx\");\n\n\n\n\nasync function getDashboardStats(userId) {\n    try {\n        const [totalManuscripts, totalPublications, pendingReviews, totalNotifications, unreadNotifications] = await Promise.all([\n            // Total manuscripts by user\n            _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.manuscript.count({\n                where: {\n                    author_id: userId\n                }\n            }),\n            // Total publications by user\n            _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.publication.count({\n                where: {\n                    author_id: userId\n                }\n            }),\n            // Pending reviews assigned to user (if they're a reviewer)\n            _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.review.count({\n                where: {\n                    reviewer_id: userId,\n                    status: \"PENDING\"\n                }\n            }),\n            // Total notifications\n            _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.notification.count({\n                where: {\n                    userId: userId\n                }\n            }),\n            // Unread notifications\n            _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.notification.count({\n                where: {\n                    userId: userId,\n                    isRead: false\n                }\n            })\n        ]);\n        // Calculate some derived stats\n        const totalViews = totalPublications * 150; // Placeholder calculation\n        const totalDownloads = totalPublications * 45; // Placeholder calculation\n        return {\n            totalManuscripts,\n            totalPublications,\n            totalViews,\n            totalDownloads,\n            pendingReviews,\n            totalNotifications,\n            unreadNotifications\n        };\n    } catch (error) {\n        console.error(\"Failed to fetch dashboard stats:\", error);\n        return {\n            totalManuscripts: 0,\n            totalPublications: 0,\n            totalViews: 0,\n            totalDownloads: 0,\n            pendingReviews: 0,\n            totalNotifications: 0,\n            unreadNotifications: 0\n        };\n    }\n}\nasync function DashboardStats() {\n    const session = await (0,_auth__WEBPACK_IMPORTED_MODULE_1__.auth)();\n    if (!session?.user?.id) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-5 mb-8\",\n            children: [\n                ...Array(4)\n            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StatsCard__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    icon: \"\\uD83D\\uDCCA\",\n                    title: \"Loading...\",\n                    value: \"--\",\n                    loading: true\n                }, i, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\DashboardStats.tsx\",\n                    lineNumber: 79,\n                    columnNumber: 11\n                }, this))\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\DashboardStats.tsx\",\n            lineNumber: 77,\n            columnNumber: 7\n        }, this);\n    }\n    const stats = await getDashboardStats(session.user.id);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-5 mb-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StatsCard__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                icon: \"\\uD83D\\uDCDA\",\n                title: \"Total Publications\",\n                value: stats.totalPublications,\n                trend: {\n                    value: 12,\n                    isPositive: true\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\DashboardStats.tsx\",\n                lineNumber: 95,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StatsCard__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                icon: \"\\uD83D\\uDC41️\",\n                title: \"Total Views\",\n                value: stats.totalViews.toLocaleString(),\n                trend: {\n                    value: 8,\n                    isPositive: true\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\DashboardStats.tsx\",\n                lineNumber: 102,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StatsCard__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                icon: \"⬇️\",\n                title: \"Downloads\",\n                value: stats.totalDownloads,\n                trend: {\n                    value: 15,\n                    isPositive: true\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\DashboardStats.tsx\",\n                lineNumber: 109,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StatsCard__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                icon: \"\\uD83D\\uDCDD\",\n                title: \"Pending Reviews\",\n                value: stats.pendingReviews,\n                trend: stats.pendingReviews > 0 ? {\n                    value: 5,\n                    isPositive: false\n                } : undefined\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\DashboardStats.tsx\",\n                lineNumber: 116,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\DashboardStats.tsx\",\n        lineNumber: 94,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/components/dashboard/DashboardStats.tsx\n");

/***/ }),

/***/ "(rsc)/./app/components/dashboard/RecentActivities.tsx":
/*!*******************************************************!*\
  !*** ./app/components/dashboard/RecentActivities.tsx ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RecentActivities)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/auth */ \"(rsc)/./auth.ts\");\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../prisma */ \"(rsc)/./prisma.ts\");\n\n\n\nasync function getRecentActivities(userId) {\n    try {\n        // Get recent notifications as activities\n        const notifications = await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.notification.findMany({\n            where: {\n                userId: userId\n            },\n            orderBy: {\n                createdAt: \"desc\"\n            },\n            take: 5\n        });\n        // Get recent manuscripts\n        const recentManuscripts = await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.manuscript.findMany({\n            where: {\n                author_id: userId\n            },\n            orderBy: {\n                createdAt: \"desc\"\n            },\n            take: 2\n        });\n        // Get recent reviews (if user is a reviewer)\n        const recentReviews = await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.review.findMany({\n            where: {\n                reviewer_id: userId\n            },\n            orderBy: {\n                createdAt: \"desc\"\n            },\n            take: 2,\n            include: {\n                manuscript: {\n                    select: {\n                        title: true\n                    }\n                }\n            }\n        });\n        // Combine and format activities\n        const activities = [\n            ...notifications.map((notification)=>({\n                    id: notification.id,\n                    type: \"notification\",\n                    icon: getNotificationIcon(notification.type),\n                    title: notification.title,\n                    description: notification.message,\n                    timestamp: notification.createdAt,\n                    isRead: notification.isRead\n                })),\n            ...recentManuscripts.map((manuscript)=>({\n                    id: manuscript.id,\n                    type: \"manuscript\",\n                    icon: \"📝\",\n                    title: \"Manuscript Created\",\n                    description: `Created \"${manuscript.title}\"`,\n                    timestamp: manuscript.createdAt,\n                    isRead: true\n                })),\n            ...recentReviews.map((review)=>({\n                    id: review.id,\n                    type: \"review\",\n                    icon: \"👀\",\n                    title: \"Review Activity\",\n                    description: `${review.status === \"REVIEW_SUBMITTED\" ? \"Completed\" : \"Started\"} review for \"${review.manuscript.title}\"`,\n                    timestamp: review.createdAt,\n                    isRead: true\n                }))\n        ];\n        // Sort by timestamp and take most recent\n        return activities.sort((a, b)=>new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()).slice(0, 5);\n    } catch (error) {\n        console.error(\"Failed to fetch recent activities:\", error);\n        return [];\n    }\n}\nfunction getNotificationIcon(type) {\n    switch(type){\n        case \"MANUSCRIPT_SUBMISSION\":\n            return \"📝\";\n        case \"REVIEW_COMPLETED\":\n            return \"✅\";\n        case \"REVIEW_REQUESTED\":\n            return \"👀\";\n        default:\n            return \"🔔\";\n    }\n}\nfunction formatTimeAgo(date) {\n    const now = new Date();\n    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));\n    if (diffInHours < 1) return \"Just now\";\n    if (diffInHours < 24) return `${diffInHours}h ago`;\n    if (diffInHours < 48) return \"Yesterday\";\n    if (diffInHours < 168) return `${Math.floor(diffInHours / 24)}d ago`;\n    return date.toLocaleDateString();\n}\nasync function RecentActivities() {\n    const session = await (0,_auth__WEBPACK_IMPORTED_MODULE_1__.auth)();\n    if (!session?.user?.id) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mb-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center mb-5\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-bold text-gray-800\",\n                        children: \"Recent Activities\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\RecentActivities.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\RecentActivities.tsx\",\n                    lineNumber: 103,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-sm p-8 text-center text-gray-400\",\n                    children: \"Please log in to view your activities.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\RecentActivities.tsx\",\n                    lineNumber: 106,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\RecentActivities.tsx\",\n            lineNumber: 102,\n            columnNumber: 7\n        }, this);\n    }\n    const activities = await getRecentActivities(session.user.id);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mb-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center mb-5\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-xl font-bold text-gray-800\",\n                    children: \"Recent Activities\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\RecentActivities.tsx\",\n                    lineNumber: 118,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\RecentActivities.tsx\",\n                lineNumber: 117,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-sm p-5\",\n                children: activities.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-4xl mb-4\",\n                            children: \"\\uD83C\\uDFAF\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\RecentActivities.tsx\",\n                            lineNumber: 124,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-medium text-gray-900 mb-2\",\n                            children: \"No recent activities\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\RecentActivities.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-400\",\n                            children: \"Your activities will appear here as you use the system.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\RecentActivities.tsx\",\n                            lineNumber: 128,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\RecentActivities.tsx\",\n                    lineNumber: 123,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: activities.map((activity, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `flex items-start space-x-4 ${index < activities.length - 1 ? \"pb-4 border-b border-gray-200\" : \"\"}`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-10 h-10 bg-blue-50 rounded-lg flex items-center justify-center text-blue-900 flex-shrink-0\",\n                                    children: activity.icon\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\RecentActivities.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 min-w-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: `font-medium ${activity.type === \"notification\" && !activity.isRead ? \"text-blue-900\" : \"text-gray-800\"}`,\n                                                    children: activity.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\RecentActivities.tsx\",\n                                                    lineNumber: 148,\n                                                    columnNumber: 21\n                                                }, this),\n                                                activity.type === \"notification\" && !activity.isRead && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-blue-500 rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\RecentActivities.tsx\",\n                                                    lineNumber: 158,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\RecentActivities.tsx\",\n                                            lineNumber: 147,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 text-sm mt-1 line-clamp-2\",\n                                            children: activity.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\RecentActivities.tsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-400 text-xs mt-2 block\",\n                                            children: formatTimeAgo(activity.timestamp)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\RecentActivities.tsx\",\n                                            lineNumber: 164,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\RecentActivities.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, activity.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\RecentActivities.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 15\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\RecentActivities.tsx\",\n                    lineNumber: 133,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\RecentActivities.tsx\",\n                lineNumber: 121,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\RecentActivities.tsx\",\n        lineNumber: 116,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/components/dashboard/RecentActivities.tsx\n");

/***/ }),

/***/ "(rsc)/./app/components/dashboard/RecentManuscripts.tsx":
/*!********************************************************!*\
  !*** ./app/components/dashboard/RecentManuscripts.tsx ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RecentManuscripts)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/auth */ \"(rsc)/./auth.ts\");\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../prisma */ \"(rsc)/./prisma.ts\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nasync function getRecentManuscripts(userId) {\n    try {\n        const manuscripts = await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.manuscript.findMany({\n            where: {\n                author_id: userId\n            },\n            orderBy: {\n                createdAt: \"desc\"\n            },\n            take: 3,\n            include: {\n                user: {\n                    select: {\n                        name: true\n                    }\n                },\n                reviews: {\n                    select: {\n                        status: true\n                    }\n                }\n            }\n        });\n        return manuscripts;\n    } catch (error) {\n        console.error(\"Failed to fetch recent manuscripts:\", error);\n        return [];\n    }\n}\nfunction getStatusInfo(manuscript) {\n    const pendingReviews = manuscript.reviews.filter((r)=>r.status === \"PENDING\").length;\n    const completedReviews = manuscript.reviews.filter((r)=>r.status === \"REVIEW_SUBMITTED\").length;\n    if (manuscript.status === \"ACCEPTED\") {\n        return {\n            label: \"Accepted\",\n            color: \"bg-green-100 text-green-600\",\n            info: `${completedReviews} reviews completed`\n        };\n    } else if (manuscript.status === \"UNDER_REVIEW\") {\n        return {\n            label: \"Under Review\",\n            color: \"bg-blue-100 text-blue-600\",\n            info: `${pendingReviews} reviews pending`\n        };\n    } else {\n        return {\n            label: \"Draft\",\n            color: \"bg-amber-100 text-amber-600\",\n            info: \"Not submitted for review\"\n        };\n    }\n}\nfunction formatDate(date) {\n    return new Intl.DateTimeFormat('en-US', {\n        month: 'short',\n        day: 'numeric',\n        year: 'numeric'\n    }).format(date);\n}\nasync function RecentManuscripts() {\n    const session = await (0,_auth__WEBPACK_IMPORTED_MODULE_1__.auth)();\n    if (!session?.user?.id) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mb-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center mb-5\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-bold text-gray-800\",\n                        children: \"Recent Manuscripts\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\RecentManuscripts.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\RecentManuscripts.tsx\",\n                    lineNumber: 79,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-8 text-gray-400\",\n                    children: \"Please log in to view your manuscripts.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\RecentManuscripts.tsx\",\n                    lineNumber: 84,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\RecentManuscripts.tsx\",\n            lineNumber: 78,\n            columnNumber: 7\n        }, this);\n    }\n    const manuscripts = await getRecentManuscripts(session.user.id);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mb-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center mb-5\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-bold text-gray-800\",\n                        children: \"Recent Manuscripts\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\RecentManuscripts.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                        href: \"/manuscripts\",\n                        className: \"text-blue-900 font-medium hover:underline\",\n                        children: \"View All\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\RecentManuscripts.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\RecentManuscripts.tsx\",\n                lineNumber: 95,\n                columnNumber: 7\n            }, this),\n            manuscripts.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-sm p-8 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-6xl mb-4\",\n                        children: \"\\uD83D\\uDCDD\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\RecentManuscripts.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                        children: \"No manuscripts yet\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\RecentManuscripts.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-400 mb-4\",\n                        children: \"Start by creating your first manuscript.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\RecentManuscripts.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                        href: \"/manuscripts/new\",\n                        className: \"inline-flex items-center px-4 py-2 bg-blue-900 text-white rounded-lg hover:bg-blue-800 transition-colors\",\n                        children: \"Create New Manuscript\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\RecentManuscripts.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\RecentManuscripts.tsx\",\n                lineNumber: 106,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-5\",\n                children: manuscripts.map((manuscript)=>{\n                    const status = getStatusInfo(manuscript);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-40 bg-gradient-to-br from-blue-500 to-blue-700 relative\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `absolute top-2 right-2 px-3 py-1 rounded-full text-xs font-medium ${status.color}`,\n                                    children: status.label\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\RecentManuscripts.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\RecentManuscripts.tsx\",\n                                lineNumber: 130,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-5\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-bold text-gray-800 mb-2 line-clamp-2\",\n                                        children: manuscript.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\RecentManuscripts.tsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between text-gray-600 text-sm mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: formatDate(manuscript.createdAt)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\RecentManuscripts.tsx\",\n                                                lineNumber: 142,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: status.info\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\RecentManuscripts.tsx\",\n                                                lineNumber: 143,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\RecentManuscripts.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                href: `/manuscripts/${manuscript.id}/edit`,\n                                                className: \"text-blue-900 font-medium text-sm hover:underline\",\n                                                children: \"Edit\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\RecentManuscripts.tsx\",\n                                                lineNumber: 146,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                href: `/manuscripts/${manuscript.id}`,\n                                                className: \"text-blue-900 font-medium text-sm hover:underline\",\n                                                children: \"View Details\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\RecentManuscripts.tsx\",\n                                                lineNumber: 152,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\RecentManuscripts.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\RecentManuscripts.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, manuscript.id, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\RecentManuscripts.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 15\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\RecentManuscripts.tsx\",\n                lineNumber: 122,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\RecentManuscripts.tsx\",\n        lineNumber: 94,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/components/dashboard/RecentManuscripts.tsx\n");

/***/ }),

/***/ "(rsc)/./app/components/dashboard/Sidebar.tsx":
/*!**********************************************!*\
  !*** ./app/components/dashboard/Sidebar.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\Sidebar.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\dashboard\\Sidebar.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./app/components/dashboard/StatsCard.tsx":
/*!************************************************!*\
  !*** ./app/components/dashboard/StatsCard.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ StatsCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction StatsCard({ icon, title, label, value, color = \"bg-blue-50 text-blue-900\", href, loading = false, trend }) {\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-sm p-5 animate-pulse\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-12 h-12 rounded-xl bg-gray-200 mb-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\StatsCard.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-8 bg-gray-200 rounded mb-2 w-16\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\StatsCard.tsx\",\n                    lineNumber: 31,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-4 bg-gray-200 rounded w-24\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\StatsCard.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\StatsCard.tsx\",\n            lineNumber: 29,\n            columnNumber: 7\n        }, this);\n    }\n    const cardContent = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `w-12 h-12 rounded-xl ${color} flex items-center justify-center text-xl mb-4`,\n                children: icon\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\StatsCard.tsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-2xl text-gray-800 font-bold\",\n                        children: value\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\StatsCard.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 9\n                    }, this),\n                    trend && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: `text-sm font-medium ${trend.isPositive ? \"text-green-600\" : \"text-red-600\"}`,\n                        children: [\n                            trend.isPositive ? \"↗\" : \"↘\",\n                            \" \",\n                            Math.abs(trend.value),\n                            \"%\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\StatsCard.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\StatsCard.tsx\",\n                lineNumber: 44,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-gray-400 text-sm\",\n                children: title || label\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\StatsCard.tsx\",\n                lineNumber: 56,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n    if (href) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n            href: href,\n            className: \"block\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow-sm p-5 hover:shadow-md transition-shadow cursor-pointer\",\n                children: cardContent\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\StatsCard.tsx\",\n                lineNumber: 63,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\StatsCard.tsx\",\n            lineNumber: 62,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm p-5 hover:shadow-md transition-shadow\",\n        children: cardContent\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\StatsCard.tsx\",\n        lineNumber: 71,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/components/dashboard/StatsCard.tsx\n");

/***/ }),

/***/ "(rsc)/./app/components/footer.tsx":
/*!***********************************!*\
  !*** ./app/components/footer.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\footer.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./app/components/greeting.tsx":
/*!*************************************!*\
  !*** ./app/components/greeting.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\greeting.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\greeting.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./app/components/home_nav.tsx":
/*!*************************************!*\
  !*** ./app/components/home_nav.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home_nav.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\components\\home_nav.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./app/context/AuthProvider.tsx":
/*!**************************************!*\
  !*** ./app/context/AuthProvider.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\context\\\\AuthProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Tina Education.org\\Review Request backend\\ARRS-NextJS\\tina-education\\app\\context\\AuthProvider.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./app/dashboard/layout.tsx":
/*!**********************************!*\
  !*** ./app/dashboard/layout.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_dashboard_Sidebar__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../components/dashboard/Sidebar */ \"(rsc)/./app/components/dashboard/Sidebar.tsx\");\n\n\nasync function DashboardLayout({ children }) {\n    // Authentication is handled by middleware\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-gray-100 pt-20\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex min-h-[calc(100vh-5rem)]\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_Sidebar__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\dashboard\\\\layout.tsx\",\n                    lineNumber: 13,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\dashboard\\\\layout.tsx\",\n                    lineNumber: 14,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\dashboard\\\\layout.tsx\",\n            lineNumber: 12,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\dashboard\\\\layout.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZGFzaGJvYXJkL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBc0Q7QUFFdkMsZUFBZUMsZ0JBQWdCLEVBQzVDQyxRQUFRLEVBR1Q7SUFDQywwQ0FBMEM7SUFFMUMscUJBQ0UsOERBQUNDO1FBQUlDLFdBQVU7a0JBQ2IsNEVBQUNEO1lBQUlDLFdBQVU7OzhCQUNiLDhEQUFDSixxRUFBT0E7Ozs7OzhCQUNSLDhEQUFDRztvQkFBSUMsV0FBVTs4QkFBVUY7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBSWpDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGJpY2hlXFxUaW5hIEVkdWNhdGlvbi5vcmdcXFJldmlldyBSZXF1ZXN0IGJhY2tlbmRcXEFSUlMtTmV4dEpTXFx0aW5hLWVkdWNhdGlvblxcYXBwXFxkYXNoYm9hcmRcXGxheW91dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFNpZGViYXIgZnJvbSBcIi4uL2NvbXBvbmVudHMvZGFzaGJvYXJkL1NpZGViYXJcIjtcblxuZXhwb3J0IGRlZmF1bHQgYXN5bmMgZnVuY3Rpb24gRGFzaGJvYXJkTGF5b3V0KHtcbiAgY2hpbGRyZW4sXG59OiB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XG59KSB7XG4gIC8vIEF1dGhlbnRpY2F0aW9uIGlzIGhhbmRsZWQgYnkgbWlkZGxld2FyZVxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmF5LTEwMCBwdC0yMFwiPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IG1pbi1oLVtjYWxjKDEwMHZoLTVyZW0pXVwiPlxuICAgICAgICA8U2lkZWJhciAvPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMVwiPntjaGlsZHJlbn08L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApO1xufVxuIl0sIm5hbWVzIjpbIlNpZGViYXIiLCJEYXNoYm9hcmRMYXlvdXQiLCJjaGlsZHJlbiIsImRpdiIsImNsYXNzTmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/dashboard/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/dashboard/page.tsx":
/*!********************************!*\
  !*** ./app/dashboard/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Dashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_greeting__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/greeting */ \"(rsc)/./app/components/greeting.tsx\");\n/* harmony import */ var _components_dashboard_DashboardStats__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/dashboard/DashboardStats */ \"(rsc)/./app/components/dashboard/DashboardStats.tsx\");\n/* harmony import */ var _components_dashboard_RecentManuscripts__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/dashboard/RecentManuscripts */ \"(rsc)/./app/components/dashboard/RecentManuscripts.tsx\");\n/* harmony import */ var _components_dashboard_RecentActivities__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../components/dashboard/RecentActivities */ \"(rsc)/./app/components/dashboard/RecentActivities.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_6__);\n\n\n\n\n\n\n\nfunction Dashboard() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-8 min-h-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_greeting__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 12,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: \"/manuscripts/new\",\n                            className: \"px-5 py-2 bg-blue-900 text-white rounded hover:bg-blue-800 transition-colors\",\n                            children: \"Create New\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 14,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 13,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 11,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_6__.Suspense, {\n                fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-5 mb-8\",\n                    children: [\n                        ...Array(4)\n                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow-sm p-5 animate-pulse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-12 h-12 rounded-xl bg-gray-200 mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 31,\n                                    columnNumber: 17\n                                }, void 0),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-8 bg-gray-200 rounded mb-2 w-16\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 32,\n                                    columnNumber: 17\n                                }, void 0),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 bg-gray-200 rounded w-24\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 33,\n                                    columnNumber: 17\n                                }, void 0)\n                            ]\n                        }, i, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 27,\n                            columnNumber: 15\n                        }, void 0))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 11\n                }, void 0),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_DashboardStats__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 23,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_6__.Suspense, {\n                fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center mb-5\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-bold text-gray-800\",\n                                    children: \"Recent Manuscripts\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 46,\n                                    columnNumber: 15\n                                }, void 0),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 w-16 bg-gray-200 rounded animate-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 49,\n                                    columnNumber: 15\n                                }, void 0)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 45,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-5\",\n                            children: [\n                                ...Array(3)\n                            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow-sm overflow-hidden animate-pulse\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-40 bg-gray-200\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 57,\n                                            columnNumber: 19\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-5\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-4 bg-gray-200 rounded mb-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 59,\n                                                    columnNumber: 21\n                                                }, void 0),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-3 bg-gray-200 rounded w-3/4 mb-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 60,\n                                                    columnNumber: 21\n                                                }, void 0),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-3 bg-gray-200 rounded w-12\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 62,\n                                                            columnNumber: 23\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-3 bg-gray-200 rounded w-16\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 63,\n                                                            columnNumber: 23\n                                                        }, void 0)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 61,\n                                                    columnNumber: 21\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 58,\n                                            columnNumber: 19\n                                        }, void 0)\n                                    ]\n                                }, i, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 53,\n                                    columnNumber: 17\n                                }, void 0))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 44,\n                    columnNumber: 11\n                }, void 0),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_RecentManuscripts__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 42,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mb-5\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-bold text-gray-800\",\n                                children: \"Analytics Overview\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 77,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"#\",\n                                className: \"text-blue-900 font-medium\",\n                                children: \"Detailed Reports\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-5\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-sm p-5 col-span-2 h-64 flex flex-col\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-bold text-gray-400 mb-5\",\n                                        children: \"Publication Performance\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 bg-gray-100 rounded flex items-center justify-center text-gray-400\",\n                                        children: \"[Views & Downloads Chart]\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-sm p-5 h-64 flex flex-col\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-bold text-gray-400 mb-5\",\n                                        children: \"Reader Demographics\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 bg-gray-100 rounded flex items-center justify-center text-gray-400\",\n                                        children: \"[Demographics Chart]\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 75,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mb-5\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-bold text-gray-800\",\n                                children: \"Upcoming Deadlines\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"#\",\n                                className: \"text-blue-900 font-medium\",\n                                children: \"View Calendar\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-sm p-5\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex mb-4 pb-4 border-b border-gray-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-blue-50 rounded-lg flex flex-col items-center justify-center mr-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-lg font-bold text-blue-900\",\n                                                    children: \"25\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 120,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-blue-900\",\n                                                    children: \"Mar\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 121,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 119,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-bold text-gray-400 mb-1\",\n                                                    children: \"Conference Paper Submission\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 124,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-400 text-sm\",\n                                                    children: \"EdTech Global Summit 2025\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 127,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 123,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex mb-4 pb-4 border-b border-gray-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-blue-50 rounded-lg flex flex-col items-center justify-center mr-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-lg font-bold text-blue-900\",\n                                                    children: \"02\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 135,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-blue-900\",\n                                                    children: \"Apr\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 136,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 134,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-bold text-gray-400 mb-1\",\n                                                    children: \"Peer Review Deadline\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 139,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-400 text-sm\",\n                                                    children: \"Educational Psychology Journal\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 142,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-blue-50 rounded-lg flex flex-col items-center justify-center mr-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-lg font-bold text-blue-900\",\n                                                    children: \"15\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 150,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-blue-900\",\n                                                    children: \"Apr\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 151,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-bold text-gray-400 mb-1\",\n                                                    children: \"Book Chapter Draft\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 154,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-400 text-sm\",\n                                                    children: \"Modern Teaching Anthology\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 157,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 117,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 106,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_6__.Suspense, {\n                fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center mb-5\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-bold text-gray-800\",\n                                children: \"Recent Activities\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 15\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 169,\n                            columnNumber: 13\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow-sm p-5\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    ...Array(4)\n                                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start space-x-4 animate-pulse\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-10 h-10 bg-gray-200 rounded-lg\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 181,\n                                                columnNumber: 21\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-4 bg-gray-200 rounded mb-2 w-3/4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 183,\n                                                        columnNumber: 23\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-3 bg-gray-200 rounded mb-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 184,\n                                                        columnNumber: 23\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-3 bg-gray-200 rounded w-16\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 185,\n                                                        columnNumber: 23\n                                                    }, void 0)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 182,\n                                                columnNumber: 21\n                                            }, void 0)\n                                        ]\n                                    }, i, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 19\n                                    }, void 0))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 15\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 174,\n                            columnNumber: 13\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 168,\n                    columnNumber: 11\n                }, void 0),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_RecentActivities__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 194,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 166,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/dashboard/page.tsx\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"249b9d197b61\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYmljaGVcXFRpbmEgRWR1Y2F0aW9uLm9yZ1xcUmV2aWV3IFJlcXVlc3QgYmFja2VuZFxcQVJSUy1OZXh0SlNcXHRpbmEtZWR1Y2F0aW9uXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiMjQ5YjlkMTk3YjYxXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _context_AuthProvider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./context/AuthProvider */ \"(rsc)/./app/context/AuthProvider.tsx\");\n/* harmony import */ var _components_home_nav__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./components/home_nav */ \"(rsc)/./app/components/home_nav.tsx\");\n/* harmony import */ var _components_footer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./components/footer */ \"(rsc)/./app/components/footer.tsx\");\n/* harmony import */ var _components_NotificationToast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./components/NotificationToast */ \"(rsc)/./app/components/NotificationToast.tsx\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\n\n\n\n\n\n\nconst metadata = {\n    title: \"Tina Education\",\n    description: \"Generated by create next app\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_6___default().variable)} ${(next_font_google_target_css_path_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_7___default().variable)} antialiased min-h-screen flex flex-col`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_AuthProvider__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_home_nav__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\layout.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\layout.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_footer__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\layout.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NotificationToast__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\layout.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\layout.tsx\",\n                lineNumber: 34,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\layout.tsx\",\n            lineNumber: 31,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\layout.tsx\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./auth.ts":
/*!*****************!*\
  !*** ./auth.ts ***!
  \*****************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   auth: () => (/* binding */ auth),\n/* harmony export */   handlers: () => (/* binding */ handlers),\n/* harmony export */   signIn: () => (/* binding */ signIn),\n/* harmony export */   signOut: () => (/* binding */ signOut)\n/* harmony export */ });\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var _auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @auth/prisma-adapter */ \"(rsc)/./node_modules/@auth/prisma-adapter/index.js\");\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/prisma */ \"(rsc)/./prisma.ts\");\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n\n\n\n\n\nconst { handlers, signIn, signOut, auth } = (0,next_auth__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    adapter: (0,_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_1__.PrismaAdapter)(_prisma__WEBPACK_IMPORTED_MODULE_2__.prisma),\n    secret: process.env.AUTH_SECRET,\n    trustHost: true,\n    pages: {\n        signIn: \"/auth/signin\"\n    },\n    providers: [\n        next_auth_providers_google__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_3__[\"default\"])({\n            credentials: {\n                username: {\n                    label: \"Username\",\n                    type: \"text\",\n                    placeholder: \"Enter UserName\"\n                },\n                password: {\n                    label: \"Password\",\n                    type: \"password\",\n                    placeholder: \"Enter Password\"\n                }\n            },\n            async authorize (credentials) {\n                const { username, password } = credentials;\n                const user = {\n                    id: \"1\",\n                    name: \"Bichesq\",\n                    email: \"<EMAIL>\"\n                };\n                if (username === user.name && password === \"nextgmail.com\") {\n                    return user;\n                } else {\n                    return null;\n                }\n            }\n        })\n    ],\n    callbacks: {\n        async authorized ({ auth, request: { nextUrl } }) {\n            // This callback is required for middleware to work properly\n            const isLoggedIn = !!auth?.user;\n            const isOnDashboard = nextUrl.pathname.startsWith(\"/dashboard\");\n            const isOnManuscripts = nextUrl.pathname.startsWith(\"/manuscripts\");\n            const isOnReviews = nextUrl.pathname.startsWith(\"/reviews\");\n            console.log(\"🔐 Authorized callback - auth:\", auth);\n            console.log(\"🔐 Authorized callback - isLoggedIn:\", isLoggedIn);\n            console.log(\"🔐 Authorized callback - pathname:\", nextUrl.pathname);\n            // Allow access to protected routes only if logged in\n            if (isOnDashboard || isOnManuscripts || isOnReviews) {\n                return isLoggedIn;\n            }\n            // Allow access to all other routes\n            return true;\n        },\n        async session ({ session, token }) {\n            console.log(\"🔐 Session callback - session:\", session);\n            console.log(\"🔐 Session callback - token:\", token);\n            if (token?.sub) {\n                session.user.id = token.sub;\n                // Fetch user role from database\n                try {\n                    const user = await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.user.findUnique({\n                        where: {\n                            id: token.sub\n                        },\n                        select: {\n                            role: true\n                        }\n                    });\n                    if (user) {\n                        session.user.role = user.role;\n                    }\n                } catch (error) {\n                    console.error(\"Failed to fetch user role:\", error);\n                }\n            }\n            return session;\n        },\n        async jwt ({ token, user }) {\n            console.log(\"🔐 JWT callback - token:\", token);\n            console.log(\"🔐 JWT callback - user:\", user);\n            if (user) {\n                token.sub = user.id;\n            }\n            return token;\n        }\n    }\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./auth.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/dashboard/layout.tsx */ \"(rsc)/./app/dashboard/layout.tsx\"));\nconst page5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/dashboard/page.tsx */ \"(rsc)/./app/dashboard/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page5, \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\dashboard\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [module4, \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\dashboard\\\\layout.tsx\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\dashboard\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/dashboard/page\",\n        pathname: \"/dashboard\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccomponents%5C%5Cdashboard%5C%5CSidebar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccomponents%5C%5Cdashboard%5C%5CSidebar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/components/dashboard/Sidebar.tsx */ \"(rsc)/./app/components/dashboard/Sidebar.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2JpY2hlJTVDJTVDVGluYSUyMEVkdWNhdGlvbi5vcmclNUMlNUNSZXZpZXclMjBSZXF1ZXN0JTIwYmFja2VuZCU1QyU1Q0FSUlMtTmV4dEpTJTVDJTVDdGluYS1lZHVjYXRpb24lNUMlNUNhcHAlNUMlNUNjb21wb25lbnRzJTVDJTVDZGFzaGJvYXJkJTVDJTVDU2lkZWJhci50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJkZWZhdWx0JTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSx3TEFBb00iLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcImRlZmF1bHRcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxiaWNoZVxcXFxUaW5hIEVkdWNhdGlvbi5vcmdcXFxcUmV2aWV3IFJlcXVlc3QgYmFja2VuZFxcXFxBUlJTLU5leHRKU1xcXFx0aW5hLWVkdWNhdGlvblxcXFxhcHBcXFxcY29tcG9uZW50c1xcXFxkYXNoYm9hcmRcXFxcU2lkZWJhci50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccomponents%5C%5Cdashboard%5C%5CSidebar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccomponents%5C%5Cfooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccomponents%5C%5Chome_nav.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccomponents%5C%5CNotificationToast.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccontext%5C%5CAuthProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccomponents%5C%5Cfooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccomponents%5C%5Chome_nav.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccomponents%5C%5CNotificationToast.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccontext%5C%5CAuthProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/components/footer.tsx */ \"(rsc)/./app/components/footer.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/components/home_nav.tsx */ \"(rsc)/./app/components/home_nav.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/components/NotificationToast.tsx */ \"(rsc)/./app/components/NotificationToast.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/context/AuthProvider.tsx */ \"(rsc)/./app/context/AuthProvider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccomponents%5C%5Cfooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccomponents%5C%5Chome_nav.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccomponents%5C%5CNotificationToast.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccontext%5C%5CAuthProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccomponents%5C%5Cgreeting.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%2C%22__esModule%22%2C%22default%22%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccomponents%5C%5Cgreeting.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%2C%22__esModule%22%2C%22default%22%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/components/greeting.tsx */ \"(rsc)/./app/components/greeting.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2JpY2hlJTVDJTVDVGluYSUyMEVkdWNhdGlvbi5vcmclNUMlNUNSZXZpZXclMjBSZXF1ZXN0JTIwYmFja2VuZCU1QyU1Q0FSUlMtTmV4dEpTJTVDJTVDdGluYS1lZHVjYXRpb24lNUMlNUNhcHAlNUMlNUNjb21wb25lbnRzJTVDJTVDZ3JlZXRpbmcudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyZGVmYXVsdCUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNiaWNoZSU1QyU1Q1RpbmElMjBFZHVjYXRpb24ub3JnJTVDJTVDUmV2aWV3JTIwUmVxdWVzdCUyMGJhY2tlbmQlNUMlNUNBUlJTLU5leHRKUyU1QyU1Q3RpbmEtZWR1Y2F0aW9uJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNhcHAtZGlyJTVDJTVDbGluay5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMiolMjIlMkMlMjJfX2VzTW9kdWxlJTIyJTJDJTIyZGVmYXVsdCUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsc0tBQTBMO0FBQzFMO0FBQ0EsZ05BQWtMIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiQzpcXFxcVXNlcnNcXFxcYmljaGVcXFxcVGluYSBFZHVjYXRpb24ub3JnXFxcXFJldmlldyBSZXF1ZXN0IGJhY2tlbmRcXFxcQVJSUy1OZXh0SlNcXFxcdGluYS1lZHVjYXRpb25cXFxcYXBwXFxcXGNvbXBvbmVudHNcXFxcZ3JlZXRpbmcudHN4XCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxiaWNoZVxcXFxUaW5hIEVkdWNhdGlvbi5vcmdcXFxcUmV2aWV3IFJlcXVlc3QgYmFja2VuZFxcXFxBUlJTLU5leHRKU1xcXFx0aW5hLWVkdWNhdGlvblxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxhcHAtZGlyXFxcXGxpbmsuanNcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccomponents%5C%5Cgreeting.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%2C%22__esModule%22%2C%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__":
/*!**********************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ ***!
  \**********************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9hcHAvZmF2aWNvbi5pY28/X19uZXh0X21ldGFkYXRhX18iLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUEsRUFBaUY7O0FBRWpGLEVBQUUsaUVBQWU7QUFDakIsdUJBQXVCO0FBQ3ZCLHFCQUFxQiw4RkFBbUI7O0FBRXhDO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxiaWNoZVxcVGluYSBFZHVjYXRpb24ub3JnXFxSZXZpZXcgUmVxdWVzdCBiYWNrZW5kXFxBUlJTLU5leHRKU1xcdGluYS1lZHVjYXRpb25cXGFwcFxcZmF2aWNvbi5pY28/X19uZXh0X21ldGFkYXRhX18iXSwic291cmNlc0NvbnRlbnQiOlsiICBpbXBvcnQgeyBmaWxsTWV0YWRhdGFTZWdtZW50IH0gZnJvbSAnbmV4dC9kaXN0L2xpYi9tZXRhZGF0YS9nZXQtbWV0YWRhdGEtcm91dGUnXG5cbiAgZXhwb3J0IGRlZmF1bHQgYXN5bmMgKHByb3BzKSA9PiB7XG4gICAgY29uc3QgaW1hZ2VEYXRhID0ge1widHlwZVwiOlwiaW1hZ2UveC1pY29uXCIsXCJzaXplc1wiOlwiMTZ4MTZcIn1cbiAgICBjb25zdCBpbWFnZVVybCA9IGZpbGxNZXRhZGF0YVNlZ21lbnQoXCIuXCIsIGF3YWl0IHByb3BzLnBhcmFtcywgXCJmYXZpY29uLmljb1wiKVxuXG4gICAgcmV0dXJuIFt7XG4gICAgICAuLi5pbWFnZURhdGEsXG4gICAgICB1cmw6IGltYWdlVXJsICsgXCJcIixcbiAgICB9XVxuICB9Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./prisma.ts":
/*!*******************!*\
  !*** ./prisma.ts ***!
  \*******************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma || new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9wcmlzbWEudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQThDO0FBRTlDLE1BQU1DLGtCQUFrQkM7QUFFakIsTUFBTUMsU0FBU0YsZ0JBQWdCRSxNQUFNLElBQUksSUFBSUgsd0RBQVlBLEdBQUc7QUFFbkUsSUFBSUksSUFBcUMsRUFBRUgsZ0JBQWdCRSxNQUFNLEdBQUdBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGJpY2hlXFxUaW5hIEVkdWNhdGlvbi5vcmdcXFJldmlldyBSZXF1ZXN0IGJhY2tlbmRcXEFSUlMtTmV4dEpTXFx0aW5hLWVkdWNhdGlvblxccHJpc21hLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFByaXNtYUNsaWVudCB9IGZyb20gXCJAcHJpc21hL2NsaWVudFwiO1xyXG5cclxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHsgcHJpc21hOiBQcmlzbWFDbGllbnQgfTtcclxuXHJcbmV4cG9ydCBjb25zdCBwcmlzbWEgPSBnbG9iYWxGb3JQcmlzbWEucHJpc21hIHx8IG5ldyBQcmlzbWFDbGllbnQoKTtcclxuXHJcbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gXCJwcm9kdWN0aW9uXCIpIGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPSBwcmlzbWE7XHJcbiJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJnbG9iYWxGb3JQcmlzbWEiLCJnbG9iYWxUaGlzIiwicHJpc21hIiwicHJvY2VzcyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./prisma.ts\n");

/***/ }),

/***/ "(ssr)/./app/components/NotificationBell.tsx":
/*!*********************************************!*\
  !*** ./app/components/NotificationBell.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NotificationBell: () => (/* binding */ NotificationBell)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_FaBell_react_icons_fa__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=FaBell!=!react-icons/fa */ \"(ssr)/./node_modules/react-icons/fa/index.mjs\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ NotificationBell auto */ \n\n\n\n\nfunction NotificationBell() {\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const [unreadCount, setUnreadCount] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(0);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"NotificationBell.useEffect\": ()=>{\n            // Wait for session to be fully loaded and authenticated\n            if (status === \"loading\" || !session?.user?.id) {\n                return;\n            }\n            const fetchUnreadCount = {\n                \"NotificationBell.useEffect.fetchUnreadCount\": async ()=>{\n                    try {\n                        const response = await fetch(\"/api/auth/notifications\", {\n                            headers: {\n                                \"Cache-Control\": \"no-cache\"\n                            }\n                        });\n                        if (response.ok) {\n                            const data = await response.json();\n                            setUnreadCount(data.unreadCount || 0);\n                        } else if (response.status === 401) {\n                            // Unauthorized - session might have expired\n                            setUnreadCount(0);\n                        } else {\n                            // Other errors - keep previous count\n                            console.warn(\"Failed to fetch notification count:\", response.status, response.statusText);\n                        }\n                    } catch (error) {\n                        // Silently handle network errors to avoid console spam\n                        if (error instanceof TypeError && error.message.includes(\"fetch\")) {\n                            // Network error - keep previous count\n                            return;\n                        }\n                        console.error(\"Failed to fetch notification count:\", error);\n                    }\n                }\n            }[\"NotificationBell.useEffect.fetchUnreadCount\"];\n            // Initial fetch with a small delay to ensure session is stable\n            const timeoutId = setTimeout(fetchUnreadCount, 100);\n            // Poll for updates every 30 seconds\n            const interval = setInterval(fetchUnreadCount, 30000);\n            return ({\n                \"NotificationBell.useEffect\": ()=>{\n                    clearTimeout(timeoutId);\n                    clearInterval(interval);\n                }\n            })[\"NotificationBell.useEffect\"];\n        }\n    }[\"NotificationBell.useEffect\"], [\n        session?.user?.id,\n        status\n    ]);\n    // Wait for session to load\n    if (status === \"loading\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-6 h-6 animate-pulse bg-gray-200 rounded\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\NotificationBell.tsx\",\n            lineNumber: 64,\n            columnNumber: 12\n        }, this);\n    }\n    // No session means user is not logged in\n    if (status === \"unauthenticated\" || !session?.user?.id) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n        href: \"/dashboard/notifications\",\n        className: \"relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBell_react_icons_fa__WEBPACK_IMPORTED_MODULE_4__.FaBell, {\n                size: 24,\n                className: \"text-gray-600 hover:text-blue-600 transition-colors\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\NotificationBell.tsx\",\n                lineNumber: 74,\n                columnNumber: 7\n            }, this),\n            unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center animate-pulse\",\n                children: unreadCount > 99 ? \"99+\" : unreadCount\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\NotificationBell.tsx\",\n                lineNumber: 80,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\NotificationBell.tsx\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/NotificationBell.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/NotificationToast.tsx":
/*!**********************************************!*\
  !*** ./app/components/NotificationToast.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotificationToast)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction NotificationToast() {\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const removeNotification = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NotificationToast.useCallback[removeNotification]\": (id)=>{\n            setNotifications({\n                \"NotificationToast.useCallback[removeNotification]\": (prev)=>prev.filter({\n                        \"NotificationToast.useCallback[removeNotification]\": (n)=>n.id !== id\n                    }[\"NotificationToast.useCallback[removeNotification]\"])\n            }[\"NotificationToast.useCallback[removeNotification]\"]);\n        }\n    }[\"NotificationToast.useCallback[removeNotification]\"], []);\n    // Function to add a new toast notification\n    const addNotification = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"NotificationToast.useCallback[addNotification]\": (notification)=>{\n            const id = `toast_${Date.now()}_${Math.random().toString(36).substring(7)}`;\n            const newNotification = {\n                ...notification,\n                id\n            };\n            setNotifications({\n                \"NotificationToast.useCallback[addNotification]\": (prev)=>[\n                        ...prev,\n                        newNotification\n                    ]\n            }[\"NotificationToast.useCallback[addNotification]\"]);\n            // Auto-remove after 5 seconds\n            setTimeout({\n                \"NotificationToast.useCallback[addNotification]\": ()=>{\n                    removeNotification(id);\n                }\n            }[\"NotificationToast.useCallback[addNotification]\"], 5000);\n        }\n    }[\"NotificationToast.useCallback[addNotification]\"], [\n        removeNotification\n    ]);\n    // Poll for new notifications (in a real app, you'd use WebSockets or Server-Sent Events)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NotificationToast.useEffect\": ()=>{\n            // Wait for session to be fully loaded and authenticated\n            if (status === \"loading\" || !session?.user?.id) return;\n            const checkForNewNotifications = {\n                \"NotificationToast.useEffect.checkForNewNotifications\": async ()=>{\n                    try {\n                        const response = await fetch(\"/api/auth/notifications\", {\n                            headers: {\n                                \"Cache-Control\": \"no-cache\"\n                            }\n                        });\n                        if (response.ok) {\n                            await response.json();\n                        // This is a simple implementation - in production you'd want to track which notifications are new\n                        // and only show toasts for truly new ones\n                        } else if (response.status === 401) {\n                            // Unauthorized - session might have expired, stop polling\n                            return;\n                        } else {\n                            console.warn(\"Failed to fetch notifications:\", response.status, response.statusText);\n                        }\n                    } catch (error) {\n                        // Silently handle network errors to avoid spamming console\n                        // Only log if it's not a network connectivity issue\n                        if (error instanceof TypeError && error.message.includes(\"fetch\")) {\n                            // Network error - don't log to avoid spam\n                            return;\n                        }\n                        console.error(\"Failed to check for notifications:\", error);\n                    }\n                }\n            }[\"NotificationToast.useEffect.checkForNewNotifications\"];\n            // Initial delay to ensure session is stable, then check every 30 seconds\n            const timeoutId = setTimeout({\n                \"NotificationToast.useEffect.timeoutId\": ()=>{\n                    checkForNewNotifications(); // Initial check\n                }\n            }[\"NotificationToast.useEffect.timeoutId\"], 1000);\n            const interval = setInterval(checkForNewNotifications, 30000);\n            return ({\n                \"NotificationToast.useEffect\": ()=>{\n                    clearTimeout(timeoutId);\n                    clearInterval(interval);\n                }\n            })[\"NotificationToast.useEffect\"];\n        }\n    }[\"NotificationToast.useEffect\"], [\n        session?.user?.id,\n        status\n    ]);\n    // Expose the addNotification function globally for other components to use\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NotificationToast.useEffect\": ()=>{\n            window.showNotificationToast = addNotification;\n            return ({\n                \"NotificationToast.useEffect\": ()=>{\n                    delete window.showNotificationToast;\n                }\n            })[\"NotificationToast.useEffect\"];\n        }\n    }[\"NotificationToast.useEffect\"], [\n        addNotification\n    ]);\n    const getToastStyles = (type)=>{\n        const baseStyles = \"p-4 rounded-lg shadow-lg border-l-4 max-w-sm\";\n        switch(type){\n            case \"success\":\n                return `${baseStyles} bg-green-50 border-green-400 text-green-800`;\n            case \"error\":\n                return `${baseStyles} bg-red-50 border-red-400 text-red-800`;\n            case \"warning\":\n                return `${baseStyles} bg-yellow-50 border-yellow-400 text-yellow-800`;\n            case \"info\":\n            default:\n                return `${baseStyles} bg-blue-50 border-blue-400 text-blue-800`;\n        }\n    };\n    const getIcon = (type)=>{\n        switch(type){\n            case \"success\":\n                return \"✅\";\n            case \"error\":\n                return \"❌\";\n            case \"warning\":\n                return \"⚠️\";\n            case \"info\":\n            default:\n                return \"ℹ️\";\n        }\n    };\n    if (notifications.length === 0) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed top-4 right-4 z-50 space-y-2\",\n        children: notifications.map((notification)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `${getToastStyles(notification.type)} animate-slide-in-right`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-lg mr-3\",\n                            children: getIcon(notification.type)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\NotificationToast.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-medium\",\n                                    children: notification.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\NotificationToast.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm mt-1\",\n                                    children: notification.message\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\NotificationToast.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\NotificationToast.tsx\",\n                            lineNumber: 142,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>removeNotification(notification.id),\n                            className: \"ml-3 text-gray-400 hover:text-gray-600\",\n                            children: \"\\xd7\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\NotificationToast.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\NotificationToast.tsx\",\n                    lineNumber: 140,\n                    columnNumber: 11\n                }, this)\n            }, notification.id, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\NotificationToast.tsx\",\n                lineNumber: 136,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\NotificationToast.tsx\",\n        lineNumber: 134,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/NotificationToast.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/authSect.tsx":
/*!*************************************!*\
  !*** ./app/components/authSect.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AuthSect)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _NotificationBell__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./NotificationBell */ \"(ssr)/./app/components/NotificationBell.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction AuthSect() {\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_1__.useSession)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const getCurrentUrl = ()=>{\n        const params = searchParams.toString();\n        return params ? `${pathname}?${params}` : pathname;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex justify-center items-center gap-5 relative text-gray-400\",\n        children: [\n            !session && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    className: \"px-5 py-2 border border-black rounded text-gray-800 hover:bg-black hover:text-white hover:bg-opacity-10 transition-colors\",\n                    onClick: ()=>(0,next_auth_react__WEBPACK_IMPORTED_MODULE_1__.signIn)(undefined, {\n                            callbackUrl: getCurrentUrl()\n                        }),\n                    children: \"Sign In\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\authSect.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false),\n            session && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NotificationBell__WEBPACK_IMPORTED_MODULE_3__.NotificationBell, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\authSect.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-gray-800 font-medium\",\n                        children: session.user?.name\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\authSect.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"px-5 py-2 border border-white rounded bg-blue-900 text-white hover:bg-black\",\n                        onClick: ()=>(0,next_auth_react__WEBPACK_IMPORTED_MODULE_1__.signOut)({\n                                callbackUrl: \"/\"\n                            }),\n                        children: \"Sign out\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\authSect.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\authSect.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/authSect.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/dashboard/Sidebar.tsx":
/*!**********************************************!*\
  !*** ./app/components/dashboard/Sidebar.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction SidebarItem({ href, icon, label }) {\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const isActive = pathname === href || href !== \"/dashboard\" && pathname.startsWith(href);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n        className: \"mb-1\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n            href: href,\n            className: `flex items-center px-5 py-3 text-gray-800 font-medium hover:bg-blue-50 hover:text-blue-900 hover:border-l-3 hover:border-blue-900 transition-colors ${isActive ? \"bg-blue-50 text-blue-900 border-l-3 border-blue-900\" : \"\"}`,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"mr-3 text-lg\",\n                    children: icon\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    children: label\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n            lineNumber: 18,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\nfunction Sidebar() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n        className: \"sticky w-64 bg-white shadow-md top-20 h-[calc(100vh-5rem)] overflow-y-auto border-r border-gray-200 flex-shrink-0\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"py-5\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SidebarItem, {\n                            href: \"/dashboard\",\n                            icon: \"\\uD83D\\uDCCA\",\n                            label: \"Dashboard\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SidebarItem, {\n                            href: \"/dashboard/publications\",\n                            icon: \"\\uD83D\\uDCDA\",\n                            label: \"My Publications\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SidebarItem, {\n                            href: \"/dashboard/manuscripts\",\n                            icon: \"\\uD83D\\uDCDD\",\n                            label: \"Manuscripts\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SidebarItem, {\n                            href: \"/dashboard/reviews\",\n                            icon: \"\\uD83D\\uDCCB\",\n                            label: \"Reviews\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SidebarItem, {\n                            href: \"/dashboard/analytics\",\n                            icon: \"\\uD83D\\uDCC8\",\n                            label: \"Analytics\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-5 pt-5 border-t border-gray-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"px-5 mb-4 text-gray-400 text-sm uppercase\",\n                            children: \"Collaboration\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SidebarItem, {\n                                    href: \"/dashboard/collaborators\",\n                                    icon: \"\\uD83D\\uDC65\",\n                                    label: \"Co-authors\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 62,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SidebarItem, {\n                                    href: \"/dashboard/messages\",\n                                    icon: \"\\uD83D\\uDCAC\",\n                                    label: \"Messages\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SidebarItem, {\n                                    href: \"/dashboard/notifications\",\n                                    icon: \"\\uD83D\\uDD14\",\n                                    label: \"Notifications\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-5 pt-5 border-t border-gray-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"px-5 mb-4 text-gray-400 text-sm uppercase\",\n                            children: \"Repository\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SidebarItem, {\n                                    href: \"/repository\",\n                                    icon: \"\\uD83D\\uDCDA\",\n                                    label: \"All Publications\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 85,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SidebarItem, {\n                                    href: \"/books\",\n                                    icon: \"\\uD83D\\uDCD6\",\n                                    label: \"Books\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SidebarItem, {\n                                    href: \"/journals\",\n                                    icon: \"\\uD83D\\uDCF0\",\n                                    label: \"Journals\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SidebarItem, {\n                                    href: \"/articles\",\n                                    icon: \"\\uD83D\\uDCC4\",\n                                    label: \"Articles\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                    lineNumber: 80,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-5 pt-5 border-t border-gray-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"px-5 mb-4 text-gray-400 text-sm uppercase\",\n                            children: \"Resources\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SidebarItem, {\n                                    href: \"/dashboard/library\",\n                                    icon: \"\\uD83D\\uDCD5\",\n                                    label: \"My Library\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SidebarItem, {\n                                    href: \"/dashboard/tools\",\n                                    icon: \"\\uD83E\\uDDF0\",\n                                    label: \"Writing Tools\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SidebarItem, {\n                                    href: \"/dashboard/calendar\",\n                                    icon: \"\\uD83D\\uDCC5\",\n                                    label: \"Calendar\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SidebarItem, {\n                                    href: \"/dashboard/settings\",\n                                    icon: \"⚙️\",\n                                    label: \"Settings\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n                    lineNumber: 96,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n            lineNumber: 36,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\dashboard\\\\Sidebar.tsx\",\n        lineNumber: 35,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/dashboard/Sidebar.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/footer.tsx":
/*!***********************************!*\
  !*** ./app/components/footer.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction Footer() {\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('');\n    const handleEmailSubmit = (e)=>{\n        // setEmail: e.target.value\n        return undefined;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-gray-900 text-white py-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto w-[90%] max-w-7xl\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-bold mb-4\",\n                                        children: \"Quick Links\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                                        lineNumber: 19,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"space-y-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: \"/\",\n                                                    className: \"text-gray-300 hover:text-white\",\n                                                    children: \"Home\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                                                    lineNumber: 22,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                                                lineNumber: 21,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: \"#\",\n                                                    className: \"text-gray-300 hover:text-white\",\n                                                    children: \"About\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                                                    lineNumber: 27,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                                                lineNumber: 26,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: \"/books\",\n                                                    className: \"text-gray-300 hover:text-white\",\n                                                    children: \"Books\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                                                    lineNumber: 32,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                                                lineNumber: 31,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: \"/journals\",\n                                                    className: \"text-gray-300 hover:text-white\",\n                                                    children: \"Journals\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                                                    lineNumber: 40,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                                                lineNumber: 39,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: \"/articles\",\n                                                    className: \"text-gray-300 hover:text-white\",\n                                                    children: \"Articles\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                                                    lineNumber: 48,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                                                lineNumber: 47,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: \"#\",\n                                                    className: \"text-gray-300 hover:text-white\",\n                                                    children: \"Privacy Policy\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                                                    lineNumber: 56,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                                                lineNumber: 55,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                                        lineNumber: 20,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                                lineNumber: 18,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                            lineNumber: 17,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-bold mb-4\",\n                                    children: \"Services\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                                    lineNumber: 65,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"#\",\n                                                className: \"text-gray-300 hover:text-white\",\n                                                children: \"Publishing\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                                                lineNumber: 68,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                                            lineNumber: 67,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"#\",\n                                                className: \"text-gray-300 hover:text-white\",\n                                                children: \"Editing\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                                                lineNumber: 73,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                                            lineNumber: 72,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                href: \"#\",\n                                                className: \"text-gray-300 hover:text-white\",\n                                                children: \"Formatting\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                                                lineNumber: 78,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                                            lineNumber: 77,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-bold mb-4\",\n                                    children: \"Newsletter Sign Up\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                                    lineNumber: 85,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"email\",\n                                            placeholder: \"Enter your Email\",\n                                            className: \"bg-gray-600 px-4 py-2 w-full rounded\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                                            lineNumber: 87,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"bg-white text-gray-900 ms-2 px-4 py-2 rounded\",\n                                            children: \"Submit\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                                            lineNumber: 92,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                    lineNumber: 16,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-8 pt-8 border-t border-gray-700 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-400\",\n                        children: \"Copyright \\xa9 2025 - Tina Education\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n                    lineNumber: 98,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n            lineNumber: 15,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\footer.tsx\",\n        lineNumber: 14,\n        columnNumber: 7\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/footer.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/greeting.tsx":
/*!*************************************!*\
  !*** ./app/components/greeting.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Greeting)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction Greeting() {\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_1__.useSession)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"text-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n            className: \"text-3xl font-bold text-gray-800 mb-4\",\n            children: [\n                \"Welcome back \",\n                session?.user?.name\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\greeting.tsx\",\n            lineNumber: 9,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\greeting.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvY29tcG9uZW50cy9ncmVldGluZy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFDNkM7QUFFOUIsU0FBU0M7SUFDdEIsTUFBTSxFQUFFQyxNQUFNQyxPQUFPLEVBQUUsR0FBR0gsMkRBQVVBO0lBRXBDLHFCQUNFLDhEQUFDSTtRQUFJQyxXQUFVO2tCQUNiLDRFQUFDQztZQUFHRCxXQUFVOztnQkFBd0M7Z0JBQ3RDRixTQUFTSSxNQUFNQzs7Ozs7Ozs7Ozs7O0FBSXJDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGJpY2hlXFxUaW5hIEVkdWNhdGlvbi5vcmdcXFJldmlldyBSZXF1ZXN0IGJhY2tlbmRcXEFSUlMtTmV4dEpTXFx0aW5hLWVkdWNhdGlvblxcYXBwXFxjb21wb25lbnRzXFxncmVldGluZy50c3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XHJcbmltcG9ydCB7IHVzZVNlc3Npb24gfSBmcm9tIFwibmV4dC1hdXRoL3JlYWN0XCI7XHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBHcmVldGluZygpIHtcclxuICBjb25zdCB7IGRhdGE6IHNlc3Npb24gfSA9IHVzZVNlc3Npb24oKTtcclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cclxuICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQtM3hsIGZvbnQtYm9sZCB0ZXh0LWdyYXktODAwIG1iLTRcIj5cclxuICAgICAgICBXZWxjb21lIGJhY2sge3Nlc3Npb24/LnVzZXI/Lm5hbWV9XHJcbiAgICAgIDwvaDE+XHJcbiAgICA8L2Rpdj5cclxuICApO1xyXG59Il0sIm5hbWVzIjpbInVzZVNlc3Npb24iLCJHcmVldGluZyIsImRhdGEiLCJzZXNzaW9uIiwiZGl2IiwiY2xhc3NOYW1lIiwiaDEiLCJ1c2VyIiwibmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./app/components/greeting.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/home_nav.tsx":
/*!*************************************!*\
  !*** ./app/components/home_nav.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HomeNav)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _authSect__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./authSect */ \"(ssr)/./app/components/authSect.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_FaBars_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=FaBars,FaTimes!=!react-icons/fa */ \"(ssr)/./node_modules/react-icons/fa/index.mjs\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction HomeNav() {\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_4__.useSession)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.usePathname)();\n    const toggleMenu = ()=>{\n        setIsMenuOpen(!isMenuOpen);\n    };\n    // Check if user is signed in and on home page\n    const isSignedInOnHomePage = session && pathname === \"/\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"fixed top-0 w-full bg-white shadow-md z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto w-[90%] max-w-7xl\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center py-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                        href: \"/\",\n                        className: \"text-2xl font-bold text-gray-800\",\n                        children: \"Tina Education\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home_nav.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"text-gray-800 text-2xl md:hidden\",\n                        onClick: toggleMenu,\n                        \"aria-label\": \"Toggle Menu\",\n                        children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBars_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_6__.FaTimes, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home_nav.tsx\",\n                            lineNumber: 33,\n                            columnNumber: 27\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaBars_FaTimes_react_icons_fa__WEBPACK_IMPORTED_MODULE_6__.FaBars, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home_nav.tsx\",\n                            lineNumber: 33,\n                            columnNumber: 41\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home_nav.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: `${isMenuOpen ? \"block\" : \"hidden\"} md:block absolute md:static top-full left-0 w-full md:w-auto bg-white md:bg-transparent shadow-md md:shadow-none`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            className: \"flex flex-col md:flex-row items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: \"ml-0 md:ml-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: isSignedInOnHomePage ? \"/dashboard\" : \"/\",\n                                        className: \"text-gray-800 font-medium\",\n                                        children: isSignedInOnHomePage ? \"Dashboard\" : \"Home\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home_nav.tsx\",\n                                        lineNumber: 42,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home_nav.tsx\",\n                                    lineNumber: 41,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: \"ml-0 md:ml-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/books\",\n                                        className: \"text-gray-800 font-medium\",\n                                        children: \"Books\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home_nav.tsx\",\n                                        lineNumber: 51,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home_nav.tsx\",\n                                    lineNumber: 50,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: \"ml-0 md:ml-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/journals\",\n                                        className: \"text-gray-800 font-medium\",\n                                        children: \"Journals\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home_nav.tsx\",\n                                        lineNumber: 56,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home_nav.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 15\n                                }, this),\n                                session && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: \"ml-0 md:ml-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/wishlist\",\n                                        className: \"text-gray-800 font-medium\",\n                                        children: \"Wishlist\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home_nav.tsx\",\n                                        lineNumber: 62,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home_nav.tsx\",\n                                    lineNumber: 61,\n                                    columnNumber: 17\n                                }, this),\n                                session && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: \"ml-0 md:ml-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/cart\",\n                                        className: \"text-gray-800 font-medium\",\n                                        children: \"Cart\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home_nav.tsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home_nav.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: \"ml-0 md:ml-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"#\",\n                                        className: \"text-gray-800 font-medium\",\n                                        children: \"Publisher with Us\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home_nav.tsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home_nav.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: \"ml-0 md:ml-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-center items-center gap-5 mb-8 md:mb-0 relative text-gray-500\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_authSect__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home_nav.tsx\",\n                                            lineNumber: 81,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home_nav.tsx\",\n                                        lineNumber: 80,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home_nav.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home_nav.tsx\",\n                            lineNumber: 40,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home_nav.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home_nav.tsx\",\n                lineNumber: 24,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home_nav.tsx\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\home_nav.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/home_nav.tsx\n");

/***/ }),

/***/ "(ssr)/./app/context/AuthProvider.tsx":
/*!**************************************!*\
  !*** ./app/context/AuthProvider.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AuthProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction AuthProvider({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_1__.SessionProvider, {\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\context\\\\AuthProvider.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvY29udGV4dC9BdXRoUHJvdmlkZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBRWtEO0FBRW5DLFNBQVNDLGFBQWEsRUFBRUMsUUFBUSxFQUFpQztJQUM5RSxxQkFDRSw4REFBQ0YsNERBQWVBO2tCQUNiRTs7Ozs7O0FBR1AiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYmljaGVcXFRpbmEgRWR1Y2F0aW9uLm9yZ1xcUmV2aWV3IFJlcXVlc3QgYmFja2VuZFxcQVJSUy1OZXh0SlNcXHRpbmEtZWR1Y2F0aW9uXFxhcHBcXGNvbnRleHRcXEF1dGhQcm92aWRlci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xyXG5cclxuaW1wb3J0IHsgU2Vzc2lvblByb3ZpZGVyIH0gZnJvbSAnbmV4dC1hdXRoL3JlYWN0JztcclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEF1dGhQcm92aWRlcih7IGNoaWxkcmVuIH06IHsgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZSB9KSB7XHJcbiAgcmV0dXJuIChcclxuICAgIDxTZXNzaW9uUHJvdmlkZXI+XHJcbiAgICAgIHtjaGlsZHJlbn1cclxuICAgIDwvU2Vzc2lvblByb3ZpZGVyPlxyXG4gICk7XHJcbn0iXSwibmFtZXMiOlsiU2Vzc2lvblByb3ZpZGVyIiwiQXV0aFByb3ZpZGVyIiwiY2hpbGRyZW4iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./app/context/AuthProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccomponents%5C%5Cdashboard%5C%5CSidebar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccomponents%5C%5Cdashboard%5C%5CSidebar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/components/dashboard/Sidebar.tsx */ \"(ssr)/./app/components/dashboard/Sidebar.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2JpY2hlJTVDJTVDVGluYSUyMEVkdWNhdGlvbi5vcmclNUMlNUNSZXZpZXclMjBSZXF1ZXN0JTIwYmFja2VuZCU1QyU1Q0FSUlMtTmV4dEpTJTVDJTVDdGluYS1lZHVjYXRpb24lNUMlNUNhcHAlNUMlNUNjb21wb25lbnRzJTVDJTVDZGFzaGJvYXJkJTVDJTVDU2lkZWJhci50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJkZWZhdWx0JTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSx3TEFBb00iLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcImRlZmF1bHRcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxiaWNoZVxcXFxUaW5hIEVkdWNhdGlvbi5vcmdcXFxcUmV2aWV3IFJlcXVlc3QgYmFja2VuZFxcXFxBUlJTLU5leHRKU1xcXFx0aW5hLWVkdWNhdGlvblxcXFxhcHBcXFxcY29tcG9uZW50c1xcXFxkYXNoYm9hcmRcXFxcU2lkZWJhci50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccomponents%5C%5Cdashboard%5C%5CSidebar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccomponents%5C%5Cfooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccomponents%5C%5Chome_nav.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccomponents%5C%5CNotificationToast.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccontext%5C%5CAuthProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccomponents%5C%5Cfooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccomponents%5C%5Chome_nav.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccomponents%5C%5CNotificationToast.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccontext%5C%5CAuthProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/components/footer.tsx */ \"(ssr)/./app/components/footer.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/components/home_nav.tsx */ \"(ssr)/./app/components/home_nav.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/components/NotificationToast.tsx */ \"(ssr)/./app/components/NotificationToast.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/context/AuthProvider.tsx */ \"(ssr)/./app/context/AuthProvider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccomponents%5C%5Cfooter.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccomponents%5C%5Chome_nav.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccomponents%5C%5CNotificationToast.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccontext%5C%5CAuthProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccomponents%5C%5Cgreeting.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%2C%22__esModule%22%2C%22default%22%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccomponents%5C%5Cgreeting.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%2C%22__esModule%22%2C%22default%22%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/components/greeting.tsx */ \"(ssr)/./app/components/greeting.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2JpY2hlJTVDJTVDVGluYSUyMEVkdWNhdGlvbi5vcmclNUMlNUNSZXZpZXclMjBSZXF1ZXN0JTIwYmFja2VuZCU1QyU1Q0FSUlMtTmV4dEpTJTVDJTVDdGluYS1lZHVjYXRpb24lNUMlNUNhcHAlNUMlNUNjb21wb25lbnRzJTVDJTVDZ3JlZXRpbmcudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyZGVmYXVsdCUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNiaWNoZSU1QyU1Q1RpbmElMjBFZHVjYXRpb24ub3JnJTVDJTVDUmV2aWV3JTIwUmVxdWVzdCUyMGJhY2tlbmQlNUMlNUNBUlJTLU5leHRKUyU1QyU1Q3RpbmEtZWR1Y2F0aW9uJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNhcHAtZGlyJTVDJTVDbGluay5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMiolMjIlMkMlMjJfX2VzTW9kdWxlJTIyJTJDJTIyZGVmYXVsdCUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsc0tBQTBMO0FBQzFMO0FBQ0EsZ05BQWtMIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiQzpcXFxcVXNlcnNcXFxcYmljaGVcXFxcVGluYSBFZHVjYXRpb24ub3JnXFxcXFJldmlldyBSZXF1ZXN0IGJhY2tlbmRcXFxcQVJSUy1OZXh0SlNcXFxcdGluYS1lZHVjYXRpb25cXFxcYXBwXFxcXGNvbXBvbmVudHNcXFxcZ3JlZXRpbmcudHN4XCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxiaWNoZVxcXFxUaW5hIEVkdWNhdGlvbi5vcmdcXFxcUmV2aWV3IFJlcXVlc3QgYmFja2VuZFxcXFxBUlJTLU5leHRKU1xcXFx0aW5hLWVkdWNhdGlvblxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxhcHAtZGlyXFxcXGxpbmsuanNcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Capp%5C%5Ccomponents%5C%5Cgreeting.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%2C%22__esModule%22%2C%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cbiche%5C%5CTina%20Education.org%5C%5CReview%20Request%20backend%5C%5CARRS-NextJS%5C%5Ctina-education%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "../../server/app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@auth","vendor-chunks/next-auth","vendor-chunks/oauth4webapi","vendor-chunks/jose","vendor-chunks/preact","vendor-chunks/preact-render-to-string","vendor-chunks/@panva","vendor-chunks/react-icons","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();