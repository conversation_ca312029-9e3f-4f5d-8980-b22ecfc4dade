/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/publications/route";
exports.ids = ["app/api/publications/route"];
exports.modules = {

/***/ "(rsc)/./app/api/publications/route.ts":
/*!***************************************!*\
  !*** ./app/api/publications/route.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/auth */ \"(rsc)/./auth.ts\");\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../prisma */ \"(rsc)/./prisma.ts\");\n/* harmony import */ var _vercel_blob__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @vercel/blob */ \"(rsc)/./node_modules/@vercel/blob/dist/index.js\");\n\n\n\n\n// GET - Get user's publications\nasync function GET() {\n    try {\n        const session = await (0,_auth__WEBPACK_IMPORTED_MODULE_1__.auth)();\n        if (!session?.user?.id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        const publications = await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.publication.findMany({\n            where: {\n                author_id: session.user.id\n            },\n            orderBy: {\n                createdAt: \"desc\"\n            },\n            include: {\n                user: {\n                    select: {\n                        name: true,\n                        email: true\n                    }\n                },\n                genre: {\n                    select: {\n                        id: true,\n                        name: true,\n                        slug: true,\n                        parent: {\n                            select: {\n                                id: true,\n                                name: true,\n                                slug: true\n                            }\n                        }\n                    }\n                }\n            }\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            publications\n        });\n    } catch (error) {\n        console.error(\"Failed to fetch publications:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to fetch publications\"\n        }, {\n            status: 500\n        });\n    }\n}\n// POST - Create new publication\nasync function POST(request) {\n    try {\n        const session = await (0,_auth__WEBPACK_IMPORTED_MODULE_1__.auth)();\n        if (!session?.user?.id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        // Handle FormData for file uploads\n        console.log(\"📝 Processing publication creation request...\");\n        const formData = await request.formData();\n        const title = formData.get(\"title\");\n        const abstract = formData.get(\"abstract\");\n        const keywords = formData.get(\"keywords\");\n        const type = formData.get(\"type\");\n        const genreId = formData.get(\"genreId\");\n        const cover = formData.get(\"cover\");\n        const publicationFile = formData.get(\"publicationFile\");\n        console.log(\"📝 Form data received:\", {\n            title,\n            abstract: abstract?.substring(0, 50) + \"...\",\n            keywords,\n            type,\n            genreId,\n            cover,\n            hasFile: !!publicationFile,\n            fileSize: publicationFile?.size || 0\n        });\n        // Validate required fields\n        if (!title?.trim()) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Title is required\"\n            }, {\n                status: 400\n            });\n        }\n        // Validate publication type\n        const validTypes = [\n            \"JOURNAL\",\n            \"ARTICLE\",\n            \"BOOK\",\n            \"EBOOK\",\n            \"AUDIOBOOK\"\n        ];\n        if (!validTypes.includes(type)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Invalid publication type\"\n            }, {\n                status: 400\n            });\n        }\n        // Validate genre if provided\n        if (genreId && genreId.trim()) {\n            const genreExists = await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.genre.findUnique({\n                where: {\n                    id: genreId\n                }\n            });\n            if (!genreExists) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: \"Invalid genre selected\"\n                }, {\n                    status: 400\n                });\n            }\n        }\n        // Handle file upload if provided\n        let fileUrl = null;\n        let fileName = null;\n        if (publicationFile && publicationFile.size > 0) {\n            // Validate file type\n            const allowedTypes = [\n                \"application/pdf\",\n                \"application/epub+zip\",\n                \"application/x-mobipocket-ebook\",\n                \"text/plain\",\n                \"application/msword\",\n                \"application/vnd.openxmlformats-officedocument.wordprocessingml.document\"\n            ];\n            if (!allowedTypes.includes(publicationFile.type)) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: \"Invalid file type. Please upload PDF, EPUB, MOBI, TXT, DOC, or DOCX files only.\"\n                }, {\n                    status: 400\n                });\n            }\n            // Validate file size (50MB limit)\n            const maxSize = 50 * 1024 * 1024; // 50MB\n            if (publicationFile.size > maxSize) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: \"File size too large. Maximum size is 50MB.\"\n                }, {\n                    status: 400\n                });\n            }\n            // Generate unique filename\n            const timestamp = Date.now();\n            const randomString = Math.random().toString(36).substring(7);\n            const fileExtension = publicationFile.name.split(\".\").pop();\n            const uniqueFilename = `publication-${timestamp}-${randomString}.${fileExtension}`;\n            try {\n                // Upload to Vercel Blob\n                const blob = await (0,_vercel_blob__WEBPACK_IMPORTED_MODULE_3__.put)(`publications/${uniqueFilename}`, publicationFile, {\n                    access: \"public\",\n                    contentType: publicationFile.type\n                });\n                fileUrl = blob.url;\n                fileName = publicationFile.name;\n                console.log(`✅ Publication file uploaded successfully: ${blob.url}`);\n            } catch (uploadError) {\n                console.error(\"❌ File upload failed:\", uploadError);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: \"Failed to upload file\"\n                }, {\n                    status: 500\n                });\n            }\n        }\n        // Create the publication using raw SQL to work around Prisma client issue\n        console.log(\"📝 Creating publication with file data...\");\n        const publicationId = `pub_${Date.now()}_${Math.random().toString(36).substring(7)}`;\n        // Use raw SQL to insert the publication with new fields\n        await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.$executeRaw`\n      INSERT INTO \"Publication\" (\n        id, title, abstract, keywords, type, \"genreId\", cover, \"fileUrl\", \"fileName\", \"author_id\", \"createdAt\", \"updatedAt\"\n      ) VALUES (\n        ${publicationId},\n        ${title.trim()},\n        ${abstract?.trim() || null},\n        ${keywords?.trim() || null},\n        ${type}::\"Pub_type\",\n        ${genreId && genreId.trim() || null},\n        ${cover?.trim() || null},\n        ${fileUrl},\n        ${fileName},\n        ${session.user.id},\n        NOW(),\n        NOW()\n      )\n    `;\n        // Fetch the created publication with relations\n        const publication = await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.publication.findUnique({\n            where: {\n                id: publicationId\n            },\n            include: {\n                user: {\n                    select: {\n                        name: true,\n                        email: true\n                    }\n                },\n                genre: {\n                    select: {\n                        id: true,\n                        name: true,\n                        slug: true,\n                        parent: {\n                            select: {\n                                id: true,\n                                name: true,\n                                slug: true\n                            }\n                        }\n                    }\n                }\n            }\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: \"Publication created successfully\",\n            publication\n        }, {\n            status: 201\n        });\n    } catch (error) {\n        console.error(\"❌ Failed to create publication:\", error);\n        console.error(\"❌ Error details:\", {\n            message: error instanceof Error ? error.message : \"Unknown error\",\n            stack: error instanceof Error ? error.stack : \"No stack trace\",\n            name: error instanceof Error ? error.name : \"Unknown error type\"\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to create publication\",\n            details: error instanceof Error ? error.message : \"Unknown error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/publications/route.ts\n");

/***/ }),

/***/ "(rsc)/./auth.ts":
/*!*****************!*\
  !*** ./auth.ts ***!
  \*****************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   auth: () => (/* binding */ auth),\n/* harmony export */   handlers: () => (/* binding */ handlers),\n/* harmony export */   signIn: () => (/* binding */ signIn),\n/* harmony export */   signOut: () => (/* binding */ signOut)\n/* harmony export */ });\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var _auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @auth/prisma-adapter */ \"(rsc)/./node_modules/@auth/prisma-adapter/index.js\");\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/prisma */ \"(rsc)/./prisma.ts\");\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n\n\n\n\n\nconst { handlers, signIn, signOut, auth } = (0,next_auth__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    adapter: (0,_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_1__.PrismaAdapter)(_prisma__WEBPACK_IMPORTED_MODULE_2__.prisma),\n    secret: process.env.AUTH_SECRET,\n    trustHost: true,\n    pages: {\n        signIn: \"/auth/signin\"\n    },\n    providers: [\n        next_auth_providers_google__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_3__[\"default\"])({\n            credentials: {\n                username: {\n                    label: \"Username\",\n                    type: \"text\",\n                    placeholder: \"Enter UserName\"\n                },\n                password: {\n                    label: \"Password\",\n                    type: \"password\",\n                    placeholder: \"Enter Password\"\n                }\n            },\n            async authorize (credentials) {\n                const { username, password } = credentials;\n                const user = {\n                    id: \"1\",\n                    name: \"Bichesq\",\n                    email: \"<EMAIL>\"\n                };\n                if (username === user.name && password === \"nextgmail.com\") {\n                    return user;\n                } else {\n                    return null;\n                }\n            }\n        })\n    ],\n    callbacks: {\n        async authorized ({ auth, request: { nextUrl } }) {\n            // This callback is required for middleware to work properly\n            const isLoggedIn = !!auth?.user;\n            const isOnDashboard = nextUrl.pathname.startsWith(\"/dashboard\");\n            const isOnManuscripts = nextUrl.pathname.startsWith(\"/manuscripts\");\n            const isOnReviews = nextUrl.pathname.startsWith(\"/reviews\");\n            console.log(\"🔐 Authorized callback - auth:\", auth);\n            console.log(\"🔐 Authorized callback - isLoggedIn:\", isLoggedIn);\n            console.log(\"🔐 Authorized callback - pathname:\", nextUrl.pathname);\n            // Allow access to protected routes only if logged in\n            if (isOnDashboard || isOnManuscripts || isOnReviews) {\n                return isLoggedIn;\n            }\n            // Allow access to all other routes\n            return true;\n        },\n        async session ({ session, token }) {\n            console.log(\"🔐 Session callback - session:\", session);\n            console.log(\"🔐 Session callback - token:\", token);\n            if (token?.sub) {\n                session.user.id = token.sub;\n                // Fetch user role from database\n                try {\n                    const user = await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.user.findUnique({\n                        where: {\n                            id: token.sub\n                        },\n                        select: {\n                            role: true\n                        }\n                    });\n                    if (user) {\n                        session.user.role = user.role;\n                    }\n                } catch (error) {\n                    console.error(\"Failed to fetch user role:\", error);\n                }\n            }\n            return session;\n        },\n        async jwt ({ token, user }) {\n            console.log(\"🔐 JWT callback - token:\", token);\n            console.log(\"🔐 JWT callback - user:\", user);\n            if (user) {\n                token.sub = user.id;\n            }\n            return token;\n        }\n    }\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./auth.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fpublications%2Froute&page=%2Fapi%2Fpublications%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fpublications%2Froute.ts&appDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fpublications%2Froute&page=%2Fapi%2Fpublications%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fpublications%2Froute.ts&appDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_biche_Tina_Education_org_Review_Request_backend_ARRS_NextJS_tina_education_app_api_publications_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/publications/route.ts */ \"(rsc)/./app/api/publications/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/publications/route\",\n        pathname: \"/api/publications\",\n        filename: \"route\",\n        bundlePath: \"app/api/publications/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\api\\\\publications\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_biche_Tina_Education_org_Review_Request_backend_ARRS_NextJS_tina_education_app_api_publications_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fpublications%2Froute&page=%2Fapi%2Fpublications%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fpublications%2Froute.ts&appDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./prisma.ts":
/*!*******************!*\
  !*** ./prisma.ts ***!
  \*******************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma || new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9wcmlzbWEudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQThDO0FBRTlDLE1BQU1DLGtCQUFrQkM7QUFFakIsTUFBTUMsU0FBU0YsZ0JBQWdCRSxNQUFNLElBQUksSUFBSUgsd0RBQVlBLEdBQUc7QUFFbkUsSUFBSUksSUFBcUMsRUFBRUgsZ0JBQWdCRSxNQUFNLEdBQUdBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGJpY2hlXFxUaW5hIEVkdWNhdGlvbi5vcmdcXFJldmlldyBSZXF1ZXN0IGJhY2tlbmRcXEFSUlMtTmV4dEpTXFx0aW5hLWVkdWNhdGlvblxccHJpc21hLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFByaXNtYUNsaWVudCB9IGZyb20gXCJAcHJpc21hL2NsaWVudFwiO1xyXG5cclxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHsgcHJpc21hOiBQcmlzbWFDbGllbnQgfTtcclxuXHJcbmV4cG9ydCBjb25zdCBwcmlzbWEgPSBnbG9iYWxGb3JQcmlzbWEucHJpc21hIHx8IG5ldyBQcmlzbWFDbGllbnQoKTtcclxuXHJcbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gXCJwcm9kdWN0aW9uXCIpIGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPSBwcmlzbWE7XHJcbiJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJnbG9iYWxGb3JQcmlzbWEiLCJnbG9iYWxUaGlzIiwicHJpc21hIiwicHJvY2VzcyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./prisma.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../../server/app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "async_hooks":
/*!******************************!*\
  !*** external "async_hooks" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("async_hooks");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "console":
/*!**************************!*\
  !*** external "console" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("console");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "diagnostics_channel":
/*!**************************************!*\
  !*** external "diagnostics_channel" ***!
  \**************************************/
/***/ ((module) => {

"use strict";
module.exports = require("diagnostics_channel");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "http2":
/*!************************!*\
  !*** external "http2" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("http2");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "node:events":
/*!******************************!*\
  !*** external "node:events" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:events");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:stream");

/***/ }),

/***/ "node:util":
/*!****************************!*\
  !*** external "node:util" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:util");

/***/ }),

/***/ "perf_hooks":
/*!*****************************!*\
  !*** external "perf_hooks" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("perf_hooks");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "stream/web":
/*!*****************************!*\
  !*** external "stream/web" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream/web");

/***/ }),

/***/ "string_decoder":
/*!*********************************!*\
  !*** external "string_decoder" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("string_decoder");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "util/types":
/*!*****************************!*\
  !*** external "util/types" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("util/types");

/***/ }),

/***/ "worker_threads":
/*!*********************************!*\
  !*** external "worker_threads" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("worker_threads");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@auth","vendor-chunks/next-auth","vendor-chunks/oauth4webapi","vendor-chunks/jose","vendor-chunks/preact","vendor-chunks/preact-render-to-string","vendor-chunks/@panva","vendor-chunks/undici","vendor-chunks/@fastify","vendor-chunks/retry","vendor-chunks/@vercel","vendor-chunks/is-node-process","vendor-chunks/throttleit","vendor-chunks/is-buffer","vendor-chunks/async-retry"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fpublications%2Froute&page=%2Fapi%2Fpublications%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fpublications%2Froute.ts&appDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();