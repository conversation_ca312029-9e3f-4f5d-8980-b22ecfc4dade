/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/manuscripts/route";
exports.ids = ["app/api/manuscripts/route"];
exports.modules = {

/***/ "(rsc)/./app/api/manuscripts/route.ts":
/*!**************************************!*\
  !*** ./app/api/manuscripts/route.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var _auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/auth */ \"(rsc)/./auth.ts\");\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../prisma */ \"(rsc)/./prisma.ts\");\n/* harmony import */ var resend__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! resend */ \"(rsc)/./node_modules/resend/dist/index.mjs\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_pdf_service__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../lib/pdf-service */ \"(rsc)/./lib/pdf-service.ts\");\n\n\n\n\n\nconst resend = new resend__WEBPACK_IMPORTED_MODULE_2__.Resend(process.env.RESEND_API_KEY);\nasync function POST(request) {\n    const session = await (0,_auth__WEBPACK_IMPORTED_MODULE_0__.auth)();\n    if (!session?.user?.id) return console.log(\"Unauthorized\"), next_server__WEBPACK_IMPORTED_MODULE_3__.NextResponse.json({\n        error: \"Unauthorized\"\n    }, {\n        status: 401\n    });\n    try {\n        const { title, abstract, content, keywords, uploadedFile, uploadedFileName } = await request.json();\n        console.log(\"📄 Starting manuscript submission process...\");\n        console.log(`📝 Title: ${title}`);\n        console.log(`👤 Author: ${session.user?.name}`);\n        // Generate and upload PDF to Vercel Blob\n        const pdfUrl = await (0,_lib_pdf_service__WEBPACK_IMPORTED_MODULE_4__.generateAndStorePdf)(content, title, session.user?.name || \"Unknown Author\");\n        if (!pdfUrl) {\n            console.log(\"❌ Failed to generate PDF\");\n            return next_server__WEBPACK_IMPORTED_MODULE_3__.NextResponse.json({\n                error: \"Failed to generate PDF\"\n            }, {\n                status: 500\n            });\n        }\n        // Create manuscript record with error logging\n        let manuscript;\n        try {\n            manuscript = await _prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.manuscript.create({\n                data: {\n                    author_id: session.user.id,\n                    title,\n                    abstract,\n                    content,\n                    keywords,\n                    pdfFile: pdfUrl,\n                    uploadedFile: uploadedFile || null,\n                    uploadedFileName: uploadedFileName || null\n                }\n            });\n        } catch (prismaError) {\n            console.error(\"Prisma manuscript.create failed:\", prismaError, {\n                author_id: session.user.id,\n                title,\n                abstract,\n                content,\n                keywords,\n                pdfFile: pdfUrl,\n                uploadedFile: uploadedFile || null,\n                uploadedFileName: uploadedFileName || null\n            });\n            return next_server__WEBPACK_IMPORTED_MODULE_3__.NextResponse.json({\n                error: \"Failed to create manuscript\",\n                details: prismaError instanceof Error ? prismaError.message : String(prismaError)\n            }, {\n                status: 500\n            });\n        }\n        // Get reviewers\n        const reviewers = await _prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.user.findMany({\n            where: {\n                role: \"REVIEWER\"\n            }\n        });\n        // Send notifications\n        console.log(`📧 Starting email notifications for ${reviewers.length} reviewers`);\n        console.log(`🔑 Resend API Key configured: ${process.env.RESEND_API_KEY ? \"Yes\" : \"No\"}`);\n        if (!Array.isArray(reviewers) || reviewers.length === 0) {\n            console.log(\"⚠️ No reviewers found. Skipping notifications.\");\n        } else {\n            await Promise.all(reviewers.map(async (reviewer, index)=>{\n                try {\n                    console.log(`📝 Processing reviewer ${index + 1}/${reviewers.length}: ${reviewer.email}`);\n                    // Create review assignment\n                    const review = await _prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.review.create({\n                        data: {\n                            manuscript_id: manuscript.id,\n                            reviewer_id: reviewer.id,\n                            content: `Review assignment for manuscript: ${manuscript.title}`,\n                            status: \"PENDING\"\n                        }\n                    });\n                    console.log(`✅ Review assignment created for ${reviewer.email}:`, review.id);\n                    // Create dashboard notification\n                    const notification = await _prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.notification.create({\n                        data: {\n                            id: `notif_${Date.now()}_${Math.random().toString(36).substring(7)}`,\n                            userId: reviewer.id,\n                            title: \"New Manuscript Submitted\",\n                            message: `New manuscript \"${manuscript.title}\" from ${session.user?.name} requires your review`,\n                            type: \"MANUSCRIPT_SUBMISSION\",\n                            relatedId: manuscript.id\n                        }\n                    });\n                    console.log(`✅ Dashboard notification created for ${reviewer.email}:`, notification.id);\n                    // Send email notification\n                    const emailResult = await resend.emails.send({\n                        from: \"Tina Education <<EMAIL>>\",\n                        to: reviewer.email,\n                        subject: `New Manuscript Review Request: \"${manuscript.title}\"`,\n                        html: `\n                <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n                  <div style=\"background-color: #f8fafc; padding: 20px; border-radius: 8px; margin-bottom: 20px;\">\n                    <h2 style=\"color: #1e40af; margin: 0;\">📝 New Manuscript Review Request</h2>\n                  </div>\n\n                  <p>Hello <strong>${reviewer.name || \"Reviewer\"}</strong>,</p>\n\n                  <p>A new manuscript has been submitted and requires your expert review:</p>\n\n                  <div style=\"background-color: #f1f5f9; padding: 15px; border-radius: 6px; margin: 20px 0;\">\n                    <h3 style=\"margin: 0 0 10px 0; color: #334155;\">📄 Manuscript Details</h3>\n                    <p style=\"margin: 5px 0;\"><strong>Title:</strong> ${manuscript.title}</p>\n                    <p style=\"margin: 5px 0;\"><strong>Author:</strong> ${session.user?.name}</p>\n                    <p style=\"margin: 5px 0;\"><strong>Abstract:</strong> ${manuscript.abstract}</p>\n                    <p style=\"margin: 5px 0;\"><strong>Keywords:</strong> ${manuscript.keywords}</p>\n                  </div>\n\n                  <p>Please log in to the Tina Education system to access the full manuscript and begin your review.</p>\n\n                  <div style=\"text-align: center; margin: 30px 0;\">\n                    <a href=\"${process.env.NEXTAUTH_URL || \"http://localhost:3000\"}/dashboard\"\n                       style=\"background-color: #1e40af; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;\">\n                      Review Manuscript\n                    </a>\n                  </div>\n\n                  <p>Thank you for your contribution to the academic review process.</p>\n\n                  <hr style=\"border: none; border-top: 1px solid #e2e8f0; margin: 30px 0;\">\n                  <p style=\"color: #64748b; font-size: 14px;\">\n                    Best regards,<br>\n                    Tina Education<br>\n                    <small>This is an automated notification from the development environment.</small>\n                  </p>\n                </div>\n              `\n                    });\n                    console.log(`✅ Email sent successfully to ${reviewer.email}:`, emailResult);\n                } catch (notifyErr) {\n                    console.error(`❌ Notification/email failed for reviewer ${reviewer.id} (${reviewer.email}):`, notifyErr);\n                    // Log more details about the error\n                    if (notifyErr instanceof Error) {\n                        console.error(`Error message: ${notifyErr.message}`);\n                        console.error(`Error stack: ${notifyErr.stack}`);\n                    }\n                }\n            }));\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_3__.NextResponse.json({\n            success: true,\n            manuscript\n        });\n    } catch (error) {\n        console.error(\"Unexpected error in manuscript creation:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_3__.NextResponse.json({\n            error: \"Internal Server Error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvYXBpL21hbnVzY3JpcHRzL3JvdXRlLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUE4QjtBQUNXO0FBQ1Q7QUFDVztBQUNvQjtBQUUvRCxNQUFNSyxTQUFTLElBQUlILDBDQUFNQSxDQUFDSSxRQUFRQyxHQUFHLENBQUNDLGNBQWM7QUFFN0MsZUFBZUMsS0FBS0MsT0FBZ0I7SUFDekMsTUFBTUMsVUFBVSxNQUFNWCwyQ0FBSUE7SUFDMUIsSUFBSSxDQUFDVyxTQUFTQyxNQUFNQyxJQUNsQixPQUNFQyxRQUFRQyxHQUFHLENBQUMsaUJBQ1paLHFEQUFZQSxDQUFDYSxJQUFJLENBQUM7UUFBRUMsT0FBTztJQUFlLEdBQUc7UUFBRUMsUUFBUTtJQUFJO0lBRy9ELElBQUk7UUFDRixNQUFNLEVBQ0pDLEtBQUssRUFDTEMsUUFBUSxFQUNSQyxPQUFPLEVBQ1BDLFFBQVEsRUFDUkMsWUFBWSxFQUNaQyxnQkFBZ0IsRUFDakIsR0FBRyxNQUFNZCxRQUFRTSxJQUFJO1FBRXRCRixRQUFRQyxHQUFHLENBQUM7UUFDWkQsUUFBUUMsR0FBRyxDQUFDLENBQUMsVUFBVSxFQUFFSSxPQUFPO1FBQ2hDTCxRQUFRQyxHQUFHLENBQUMsQ0FBQyxXQUFXLEVBQUVKLFFBQVFDLElBQUksRUFBRWEsTUFBTTtRQUU5Qyx5Q0FBeUM7UUFDekMsTUFBTUMsU0FBUyxNQUFNdEIscUVBQW1CQSxDQUN0Q2lCLFNBQ0FGLE9BQ0FSLFFBQVFDLElBQUksRUFBRWEsUUFBUTtRQUd4QixJQUFJLENBQUNDLFFBQVE7WUFDWFosUUFBUUMsR0FBRyxDQUFDO1lBQ1osT0FBT1oscURBQVlBLENBQUNhLElBQUksQ0FDdEI7Z0JBQUVDLE9BQU87WUFBeUIsR0FDbEM7Z0JBQUVDLFFBQVE7WUFBSTtRQUVsQjtRQUVBLDhDQUE4QztRQUM5QyxJQUFJUztRQUNKLElBQUk7WUFDRkEsYUFBYSxNQUFNMUIsMkNBQU1BLENBQUMwQixVQUFVLENBQUNDLE1BQU0sQ0FBQztnQkFDMUNDLE1BQU07b0JBQ0pDLFdBQVduQixRQUFRQyxJQUFJLENBQUNDLEVBQUU7b0JBQzFCTTtvQkFDQUM7b0JBQ0FDO29CQUNBQztvQkFDQVMsU0FBU0w7b0JBQ1RILGNBQWNBLGdCQUFnQjtvQkFDOUJDLGtCQUFrQkEsb0JBQW9CO2dCQUN4QztZQUNGO1FBQ0YsRUFBRSxPQUFPUSxhQUFzQjtZQUM3QmxCLFFBQVFHLEtBQUssQ0FBQyxvQ0FBb0NlLGFBQWE7Z0JBQzdERixXQUFXbkIsUUFBUUMsSUFBSSxDQUFDQyxFQUFFO2dCQUMxQk07Z0JBQ0FDO2dCQUNBQztnQkFDQUM7Z0JBQ0FTLFNBQVNMO2dCQUNUSCxjQUFjQSxnQkFBZ0I7Z0JBQzlCQyxrQkFBa0JBLG9CQUFvQjtZQUN4QztZQUNBLE9BQU9yQixxREFBWUEsQ0FBQ2EsSUFBSSxDQUN0QjtnQkFDRUMsT0FBTztnQkFDUGdCLFNBQ0VELHVCQUF1QkUsUUFDbkJGLFlBQVlHLE9BQU8sR0FDbkJDLE9BQU9KO1lBQ2YsR0FDQTtnQkFBRWQsUUFBUTtZQUFJO1FBRWxCO1FBRUEsZ0JBQWdCO1FBQ2hCLE1BQU1tQixZQUFZLE1BQU1wQywyQ0FBTUEsQ0FBQ1csSUFBSSxDQUFDMEIsUUFBUSxDQUFDO1lBQzNDQyxPQUFPO2dCQUFFQyxNQUFNO1lBQVc7UUFDNUI7UUFFQSxxQkFBcUI7UUFDckIxQixRQUFRQyxHQUFHLENBQ1QsQ0FBQyxvQ0FBb0MsRUFBRXNCLFVBQVVJLE1BQU0sQ0FBQyxVQUFVLENBQUM7UUFFckUzQixRQUFRQyxHQUFHLENBQ1QsQ0FBQyw4QkFBOEIsRUFBRVQsUUFBUUMsR0FBRyxDQUFDQyxjQUFjLEdBQUcsUUFBUSxNQUFNO1FBRzlFLElBQUksQ0FBQ2tDLE1BQU1DLE9BQU8sQ0FBQ04sY0FBY0EsVUFBVUksTUFBTSxLQUFLLEdBQUc7WUFDdkQzQixRQUFRQyxHQUFHLENBQUM7UUFDZCxPQUFPO1lBQ0wsTUFBTTZCLFFBQVFDLEdBQUcsQ0FDZlIsVUFBVVMsR0FBRyxDQUFDLE9BQU9DLFVBQVVDO2dCQUM3QixJQUFJO29CQUNGbEMsUUFBUUMsR0FBRyxDQUNULENBQUMsdUJBQXVCLEVBQUVpQyxRQUFRLEVBQUUsQ0FBQyxFQUFFWCxVQUFVSSxNQUFNLENBQUMsRUFBRSxFQUFFTSxTQUFTRSxLQUFLLEVBQUU7b0JBRzlFLDJCQUEyQjtvQkFDM0IsTUFBTUMsU0FBUyxNQUFNakQsMkNBQU1BLENBQUNpRCxNQUFNLENBQUN0QixNQUFNLENBQUM7d0JBQ3hDQyxNQUFNOzRCQUNKc0IsZUFBZXhCLFdBQVdkLEVBQUU7NEJBQzVCdUMsYUFBYUwsU0FBU2xDLEVBQUU7NEJBQ3hCUSxTQUFTLENBQUMsa0NBQWtDLEVBQUVNLFdBQVdSLEtBQUssRUFBRTs0QkFDaEVELFFBQVE7d0JBQ1Y7b0JBQ0Y7b0JBQ0FKLFFBQVFDLEdBQUcsQ0FDVCxDQUFDLGdDQUFnQyxFQUFFZ0MsU0FBU0UsS0FBSyxDQUFDLENBQUMsQ0FBQyxFQUNwREMsT0FBT3JDLEVBQUU7b0JBR1gsZ0NBQWdDO29CQUNoQyxNQUFNd0MsZUFBZSxNQUFNcEQsMkNBQU1BLENBQUNvRCxZQUFZLENBQUN6QixNQUFNLENBQUM7d0JBQ3BEQyxNQUFNOzRCQUNKaEIsSUFBSSxDQUFDLE1BQU0sRUFBRXlDLEtBQUtDLEdBQUcsR0FBRyxDQUFDLEVBQUVDLEtBQUtDLE1BQU0sR0FBR0MsUUFBUSxDQUFDLElBQUlDLFNBQVMsQ0FBQyxJQUFJOzRCQUNwRUMsUUFBUWIsU0FBU2xDLEVBQUU7NEJBQ25CTSxPQUFPOzRCQUNQZ0IsU0FBUyxDQUFDLGdCQUFnQixFQUFFUixXQUFXUixLQUFLLENBQUMsT0FBTyxFQUFFUixRQUFRQyxJQUFJLEVBQUVhLEtBQUsscUJBQXFCLENBQUM7NEJBQy9Gb0MsTUFBTTs0QkFDTkMsV0FBV25DLFdBQVdkLEVBQUU7d0JBQzFCO29CQUNGO29CQUNBQyxRQUFRQyxHQUFHLENBQ1QsQ0FBQyxxQ0FBcUMsRUFBRWdDLFNBQVNFLEtBQUssQ0FBQyxDQUFDLENBQUMsRUFDekRJLGFBQWF4QyxFQUFFO29CQUdqQiwwQkFBMEI7b0JBQzFCLE1BQU1rRCxjQUFjLE1BQU0xRCxPQUFPMkQsTUFBTSxDQUFDQyxJQUFJLENBQUM7d0JBQzNDQyxNQUFNO3dCQUNOQyxJQUFJcEIsU0FBU0UsS0FBSzt3QkFDbEJtQixTQUFTLENBQUMsZ0NBQWdDLEVBQUV6QyxXQUFXUixLQUFLLENBQUMsQ0FBQyxDQUFDO3dCQUMvRGtELE1BQU0sQ0FBQzs7Ozs7O21DQU1jLEVBQUV0QixTQUFTdEIsSUFBSSxJQUFJLFdBQVc7Ozs7OztzRUFNSyxFQUFFRSxXQUFXUixLQUFLLENBQUM7dUVBQ2xCLEVBQUVSLFFBQVFDLElBQUksRUFBRWEsS0FBSzt5RUFDbkIsRUFBRUUsV0FBV1AsUUFBUSxDQUFDO3lFQUN0QixFQUFFTyxXQUFXTCxRQUFRLENBQUM7Ozs7Ozs2QkFNbEUsRUFBRWhCLFFBQVFDLEdBQUcsQ0FBQytELFlBQVksSUFBSSx3QkFBd0I7Ozs7Ozs7Ozs7Ozs7OztjQWVyRSxDQUFDO29CQUNIO29CQUNBeEQsUUFBUUMsR0FBRyxDQUNULENBQUMsNkJBQTZCLEVBQUVnQyxTQUFTRSxLQUFLLENBQUMsQ0FBQyxDQUFDLEVBQ2pEYztnQkFFSixFQUFFLE9BQU9RLFdBQVc7b0JBQ2xCekQsUUFBUUcsS0FBSyxDQUNYLENBQUMseUNBQXlDLEVBQUU4QixTQUFTbEMsRUFBRSxDQUFDLEVBQUUsRUFBRWtDLFNBQVNFLEtBQUssQ0FBQyxFQUFFLENBQUMsRUFDOUVzQjtvQkFFRixtQ0FBbUM7b0JBQ25DLElBQUlBLHFCQUFxQnJDLE9BQU87d0JBQzlCcEIsUUFBUUcsS0FBSyxDQUFDLENBQUMsZUFBZSxFQUFFc0QsVUFBVXBDLE9BQU8sRUFBRTt3QkFDbkRyQixRQUFRRyxLQUFLLENBQUMsQ0FBQyxhQUFhLEVBQUVzRCxVQUFVQyxLQUFLLEVBQUU7b0JBQ2pEO2dCQUNGO1lBQ0Y7UUFFSjtRQUVBLE9BQU9yRSxxREFBWUEsQ0FBQ2EsSUFBSSxDQUFDO1lBQUV5RCxTQUFTO1lBQU05QztRQUFXO0lBQ3ZELEVBQUUsT0FBT1YsT0FBTztRQUNkSCxRQUFRRyxLQUFLLENBQUMsNENBQTRDQTtRQUMxRCxPQUFPZCxxREFBWUEsQ0FBQ2EsSUFBSSxDQUN0QjtZQUFFQyxPQUFPO1FBQXdCLEdBQ2pDO1lBQUVDLFFBQVE7UUFBSTtJQUVsQjtBQUNGIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGJpY2hlXFxUaW5hIEVkdWNhdGlvbi5vcmdcXFJldmlldyBSZXF1ZXN0IGJhY2tlbmRcXEFSUlMtTmV4dEpTXFx0aW5hLWVkdWNhdGlvblxcYXBwXFxhcGlcXG1hbnVzY3JpcHRzXFxyb3V0ZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBhdXRoIH0gZnJvbSBcIkAvYXV0aFwiO1xyXG5pbXBvcnQgeyBwcmlzbWEgfSBmcm9tIFwiLi4vLi4vLi4vcHJpc21hXCI7XHJcbmltcG9ydCB7IFJlc2VuZCB9IGZyb20gXCJyZXNlbmRcIjtcclxuaW1wb3J0IHsgTmV4dFJlc3BvbnNlIH0gZnJvbSBcIm5leHQvc2VydmVyXCI7XHJcbmltcG9ydCB7IGdlbmVyYXRlQW5kU3RvcmVQZGYgfSBmcm9tIFwiLi4vLi4vLi4vbGliL3BkZi1zZXJ2aWNlXCI7XHJcblxyXG5jb25zdCByZXNlbmQgPSBuZXcgUmVzZW5kKHByb2Nlc3MuZW52LlJFU0VORF9BUElfS0VZKTtcclxuXHJcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBQT1NUKHJlcXVlc3Q6IFJlcXVlc3QpIHtcclxuICBjb25zdCBzZXNzaW9uID0gYXdhaXQgYXV0aCgpO1xyXG4gIGlmICghc2Vzc2lvbj8udXNlcj8uaWQpXHJcbiAgICByZXR1cm4gKFxyXG4gICAgICBjb25zb2xlLmxvZyhcIlVuYXV0aG9yaXplZFwiKSxcclxuICAgICAgTmV4dFJlc3BvbnNlLmpzb24oeyBlcnJvcjogXCJVbmF1dGhvcml6ZWRcIiB9LCB7IHN0YXR1czogNDAxIH0pXHJcbiAgICApO1xyXG5cclxuICB0cnkge1xyXG4gICAgY29uc3Qge1xyXG4gICAgICB0aXRsZSxcclxuICAgICAgYWJzdHJhY3QsXHJcbiAgICAgIGNvbnRlbnQsXHJcbiAgICAgIGtleXdvcmRzLFxyXG4gICAgICB1cGxvYWRlZEZpbGUsXHJcbiAgICAgIHVwbG9hZGVkRmlsZU5hbWUsXHJcbiAgICB9ID0gYXdhaXQgcmVxdWVzdC5qc29uKCk7XHJcblxyXG4gICAgY29uc29sZS5sb2coXCLwn5OEIFN0YXJ0aW5nIG1hbnVzY3JpcHQgc3VibWlzc2lvbiBwcm9jZXNzLi4uXCIpO1xyXG4gICAgY29uc29sZS5sb2coYPCfk50gVGl0bGU6ICR7dGl0bGV9YCk7XHJcbiAgICBjb25zb2xlLmxvZyhg8J+RpCBBdXRob3I6ICR7c2Vzc2lvbi51c2VyPy5uYW1lfWApO1xyXG5cclxuICAgIC8vIEdlbmVyYXRlIGFuZCB1cGxvYWQgUERGIHRvIFZlcmNlbCBCbG9iXHJcbiAgICBjb25zdCBwZGZVcmwgPSBhd2FpdCBnZW5lcmF0ZUFuZFN0b3JlUGRmKFxyXG4gICAgICBjb250ZW50LFxyXG4gICAgICB0aXRsZSxcclxuICAgICAgc2Vzc2lvbi51c2VyPy5uYW1lIHx8IFwiVW5rbm93biBBdXRob3JcIlxyXG4gICAgKTtcclxuXHJcbiAgICBpZiAoIXBkZlVybCkge1xyXG4gICAgICBjb25zb2xlLmxvZyhcIuKdjCBGYWlsZWQgdG8gZ2VuZXJhdGUgUERGXCIpO1xyXG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXHJcbiAgICAgICAgeyBlcnJvcjogXCJGYWlsZWQgdG8gZ2VuZXJhdGUgUERGXCIgfSxcclxuICAgICAgICB7IHN0YXR1czogNTAwIH1cclxuICAgICAgKTtcclxuICAgIH1cclxuXHJcbiAgICAvLyBDcmVhdGUgbWFudXNjcmlwdCByZWNvcmQgd2l0aCBlcnJvciBsb2dnaW5nXHJcbiAgICBsZXQgbWFudXNjcmlwdDtcclxuICAgIHRyeSB7XHJcbiAgICAgIG1hbnVzY3JpcHQgPSBhd2FpdCBwcmlzbWEubWFudXNjcmlwdC5jcmVhdGUoe1xyXG4gICAgICAgIGRhdGE6IHtcclxuICAgICAgICAgIGF1dGhvcl9pZDogc2Vzc2lvbi51c2VyLmlkLFxyXG4gICAgICAgICAgdGl0bGUsXHJcbiAgICAgICAgICBhYnN0cmFjdCxcclxuICAgICAgICAgIGNvbnRlbnQsXHJcbiAgICAgICAgICBrZXl3b3JkcyxcclxuICAgICAgICAgIHBkZkZpbGU6IHBkZlVybCxcclxuICAgICAgICAgIHVwbG9hZGVkRmlsZTogdXBsb2FkZWRGaWxlIHx8IG51bGwsXHJcbiAgICAgICAgICB1cGxvYWRlZEZpbGVOYW1lOiB1cGxvYWRlZEZpbGVOYW1lIHx8IG51bGwsXHJcbiAgICAgICAgfSxcclxuICAgICAgfSk7XHJcbiAgICB9IGNhdGNoIChwcmlzbWFFcnJvcjogdW5rbm93bikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKFwiUHJpc21hIG1hbnVzY3JpcHQuY3JlYXRlIGZhaWxlZDpcIiwgcHJpc21hRXJyb3IsIHtcclxuICAgICAgICBhdXRob3JfaWQ6IHNlc3Npb24udXNlci5pZCxcclxuICAgICAgICB0aXRsZSxcclxuICAgICAgICBhYnN0cmFjdCxcclxuICAgICAgICBjb250ZW50LFxyXG4gICAgICAgIGtleXdvcmRzLFxyXG4gICAgICAgIHBkZkZpbGU6IHBkZlVybCxcclxuICAgICAgICB1cGxvYWRlZEZpbGU6IHVwbG9hZGVkRmlsZSB8fCBudWxsLFxyXG4gICAgICAgIHVwbG9hZGVkRmlsZU5hbWU6IHVwbG9hZGVkRmlsZU5hbWUgfHwgbnVsbCxcclxuICAgICAgfSk7XHJcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcclxuICAgICAgICB7XHJcbiAgICAgICAgICBlcnJvcjogXCJGYWlsZWQgdG8gY3JlYXRlIG1hbnVzY3JpcHRcIixcclxuICAgICAgICAgIGRldGFpbHM6XHJcbiAgICAgICAgICAgIHByaXNtYUVycm9yIGluc3RhbmNlb2YgRXJyb3JcclxuICAgICAgICAgICAgICA/IHByaXNtYUVycm9yLm1lc3NhZ2VcclxuICAgICAgICAgICAgICA6IFN0cmluZyhwcmlzbWFFcnJvciksXHJcbiAgICAgICAgfSxcclxuICAgICAgICB7IHN0YXR1czogNTAwIH1cclxuICAgICAgKTtcclxuICAgIH1cclxuXHJcbiAgICAvLyBHZXQgcmV2aWV3ZXJzXHJcbiAgICBjb25zdCByZXZpZXdlcnMgPSBhd2FpdCBwcmlzbWEudXNlci5maW5kTWFueSh7XHJcbiAgICAgIHdoZXJlOiB7IHJvbGU6IFwiUkVWSUVXRVJcIiB9LFxyXG4gICAgfSk7XHJcblxyXG4gICAgLy8gU2VuZCBub3RpZmljYXRpb25zXHJcbiAgICBjb25zb2xlLmxvZyhcclxuICAgICAgYPCfk6cgU3RhcnRpbmcgZW1haWwgbm90aWZpY2F0aW9ucyBmb3IgJHtyZXZpZXdlcnMubGVuZ3RofSByZXZpZXdlcnNgXHJcbiAgICApO1xyXG4gICAgY29uc29sZS5sb2coXHJcbiAgICAgIGDwn5SRIFJlc2VuZCBBUEkgS2V5IGNvbmZpZ3VyZWQ6ICR7cHJvY2Vzcy5lbnYuUkVTRU5EX0FQSV9LRVkgPyBcIlllc1wiIDogXCJOb1wifWBcclxuICAgICk7XHJcblxyXG4gICAgaWYgKCFBcnJheS5pc0FycmF5KHJldmlld2VycykgfHwgcmV2aWV3ZXJzLmxlbmd0aCA9PT0gMCkge1xyXG4gICAgICBjb25zb2xlLmxvZyhcIuKaoO+4jyBObyByZXZpZXdlcnMgZm91bmQuIFNraXBwaW5nIG5vdGlmaWNhdGlvbnMuXCIpO1xyXG4gICAgfSBlbHNlIHtcclxuICAgICAgYXdhaXQgUHJvbWlzZS5hbGwoXHJcbiAgICAgICAgcmV2aWV3ZXJzLm1hcChhc3luYyAocmV2aWV3ZXIsIGluZGV4KSA9PiB7XHJcbiAgICAgICAgICB0cnkge1xyXG4gICAgICAgICAgICBjb25zb2xlLmxvZyhcclxuICAgICAgICAgICAgICBg8J+TnSBQcm9jZXNzaW5nIHJldmlld2VyICR7aW5kZXggKyAxfS8ke3Jldmlld2Vycy5sZW5ndGh9OiAke3Jldmlld2VyLmVtYWlsfWBcclxuICAgICAgICAgICAgKTtcclxuXHJcbiAgICAgICAgICAgIC8vIENyZWF0ZSByZXZpZXcgYXNzaWdubWVudFxyXG4gICAgICAgICAgICBjb25zdCByZXZpZXcgPSBhd2FpdCBwcmlzbWEucmV2aWV3LmNyZWF0ZSh7XHJcbiAgICAgICAgICAgICAgZGF0YToge1xyXG4gICAgICAgICAgICAgICAgbWFudXNjcmlwdF9pZDogbWFudXNjcmlwdC5pZCxcclxuICAgICAgICAgICAgICAgIHJldmlld2VyX2lkOiByZXZpZXdlci5pZCxcclxuICAgICAgICAgICAgICAgIGNvbnRlbnQ6IGBSZXZpZXcgYXNzaWdubWVudCBmb3IgbWFudXNjcmlwdDogJHttYW51c2NyaXB0LnRpdGxlfWAsXHJcbiAgICAgICAgICAgICAgICBzdGF0dXM6IFwiUEVORElOR1wiLFxyXG4gICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgIH0pO1xyXG4gICAgICAgICAgICBjb25zb2xlLmxvZyhcclxuICAgICAgICAgICAgICBg4pyFIFJldmlldyBhc3NpZ25tZW50IGNyZWF0ZWQgZm9yICR7cmV2aWV3ZXIuZW1haWx9OmAsXHJcbiAgICAgICAgICAgICAgcmV2aWV3LmlkXHJcbiAgICAgICAgICAgICk7XHJcblxyXG4gICAgICAgICAgICAvLyBDcmVhdGUgZGFzaGJvYXJkIG5vdGlmaWNhdGlvblxyXG4gICAgICAgICAgICBjb25zdCBub3RpZmljYXRpb24gPSBhd2FpdCBwcmlzbWEubm90aWZpY2F0aW9uLmNyZWF0ZSh7XHJcbiAgICAgICAgICAgICAgZGF0YToge1xyXG4gICAgICAgICAgICAgICAgaWQ6IGBub3RpZl8ke0RhdGUubm93KCl9XyR7TWF0aC5yYW5kb20oKS50b1N0cmluZygzNikuc3Vic3RyaW5nKDcpfWAsXHJcbiAgICAgICAgICAgICAgICB1c2VySWQ6IHJldmlld2VyLmlkLFxyXG4gICAgICAgICAgICAgICAgdGl0bGU6IFwiTmV3IE1hbnVzY3JpcHQgU3VibWl0dGVkXCIsXHJcbiAgICAgICAgICAgICAgICBtZXNzYWdlOiBgTmV3IG1hbnVzY3JpcHQgXCIke21hbnVzY3JpcHQudGl0bGV9XCIgZnJvbSAke3Nlc3Npb24udXNlcj8ubmFtZX0gcmVxdWlyZXMgeW91ciByZXZpZXdgLFxyXG4gICAgICAgICAgICAgICAgdHlwZTogXCJNQU5VU0NSSVBUX1NVQk1JU1NJT05cIixcclxuICAgICAgICAgICAgICAgIHJlbGF0ZWRJZDogbWFudXNjcmlwdC5pZCxcclxuICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICB9KTtcclxuICAgICAgICAgICAgY29uc29sZS5sb2coXHJcbiAgICAgICAgICAgICAgYOKchSBEYXNoYm9hcmQgbm90aWZpY2F0aW9uIGNyZWF0ZWQgZm9yICR7cmV2aWV3ZXIuZW1haWx9OmAsXHJcbiAgICAgICAgICAgICAgbm90aWZpY2F0aW9uLmlkXHJcbiAgICAgICAgICAgICk7XHJcblxyXG4gICAgICAgICAgICAvLyBTZW5kIGVtYWlsIG5vdGlmaWNhdGlvblxyXG4gICAgICAgICAgICBjb25zdCBlbWFpbFJlc3VsdCA9IGF3YWl0IHJlc2VuZC5lbWFpbHMuc2VuZCh7XHJcbiAgICAgICAgICAgICAgZnJvbTogXCJUaW5hIEVkdWNhdGlvbiA8b25ib2FyZGluZ0ByZXNlbmQuZGV2PlwiLFxyXG4gICAgICAgICAgICAgIHRvOiByZXZpZXdlci5lbWFpbCEsXHJcbiAgICAgICAgICAgICAgc3ViamVjdDogYE5ldyBNYW51c2NyaXB0IFJldmlldyBSZXF1ZXN0OiBcIiR7bWFudXNjcmlwdC50aXRsZX1cImAsXHJcbiAgICAgICAgICAgICAgaHRtbDogYFxyXG4gICAgICAgICAgICAgICAgPGRpdiBzdHlsZT1cImZvbnQtZmFtaWx5OiBBcmlhbCwgc2Fucy1zZXJpZjsgbWF4LXdpZHRoOiA2MDBweDsgbWFyZ2luOiAwIGF1dG87XCI+XHJcbiAgICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9XCJiYWNrZ3JvdW5kLWNvbG9yOiAjZjhmYWZjOyBwYWRkaW5nOiAyMHB4OyBib3JkZXItcmFkaXVzOiA4cHg7IG1hcmdpbi1ib3R0b206IDIwcHg7XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPGgyIHN0eWxlPVwiY29sb3I6ICMxZTQwYWY7IG1hcmdpbjogMDtcIj7wn5OdIE5ldyBNYW51c2NyaXB0IFJldmlldyBSZXF1ZXN0PC9oMj5cclxuICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICAgICAgICA8cD5IZWxsbyA8c3Ryb25nPiR7cmV2aWV3ZXIubmFtZSB8fCBcIlJldmlld2VyXCJ9PC9zdHJvbmc+LDwvcD5cclxuXHJcbiAgICAgICAgICAgICAgICAgIDxwPkEgbmV3IG1hbnVzY3JpcHQgaGFzIGJlZW4gc3VibWl0dGVkIGFuZCByZXF1aXJlcyB5b3VyIGV4cGVydCByZXZpZXc6PC9wPlxyXG5cclxuICAgICAgICAgICAgICAgICAgPGRpdiBzdHlsZT1cImJhY2tncm91bmQtY29sb3I6ICNmMWY1Zjk7IHBhZGRpbmc6IDE1cHg7IGJvcmRlci1yYWRpdXM6IDZweDsgbWFyZ2luOiAyMHB4IDA7XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPGgzIHN0eWxlPVwibWFyZ2luOiAwIDAgMTBweCAwOyBjb2xvcjogIzMzNDE1NTtcIj7wn5OEIE1hbnVzY3JpcHQgRGV0YWlsczwvaDM+XHJcbiAgICAgICAgICAgICAgICAgICAgPHAgc3R5bGU9XCJtYXJnaW46IDVweCAwO1wiPjxzdHJvbmc+VGl0bGU6PC9zdHJvbmc+ICR7bWFudXNjcmlwdC50aXRsZX08L3A+XHJcbiAgICAgICAgICAgICAgICAgICAgPHAgc3R5bGU9XCJtYXJnaW46IDVweCAwO1wiPjxzdHJvbmc+QXV0aG9yOjwvc3Ryb25nPiAke3Nlc3Npb24udXNlcj8ubmFtZX08L3A+XHJcbiAgICAgICAgICAgICAgICAgICAgPHAgc3R5bGU9XCJtYXJnaW46IDVweCAwO1wiPjxzdHJvbmc+QWJzdHJhY3Q6PC9zdHJvbmc+ICR7bWFudXNjcmlwdC5hYnN0cmFjdH08L3A+XHJcbiAgICAgICAgICAgICAgICAgICAgPHAgc3R5bGU9XCJtYXJnaW46IDVweCAwO1wiPjxzdHJvbmc+S2V5d29yZHM6PC9zdHJvbmc+ICR7bWFudXNjcmlwdC5rZXl3b3Jkc308L3A+XHJcbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgICAgICAgPHA+UGxlYXNlIGxvZyBpbiB0byB0aGUgVGluYSBFZHVjYXRpb24gc3lzdGVtIHRvIGFjY2VzcyB0aGUgZnVsbCBtYW51c2NyaXB0IGFuZCBiZWdpbiB5b3VyIHJldmlldy48L3A+XHJcblxyXG4gICAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPVwidGV4dC1hbGlnbjogY2VudGVyOyBtYXJnaW46IDMwcHggMDtcIj5cclxuICAgICAgICAgICAgICAgICAgICA8YSBocmVmPVwiJHtwcm9jZXNzLmVudi5ORVhUQVVUSF9VUkwgfHwgXCJodHRwOi8vbG9jYWxob3N0OjMwMDBcIn0vZGFzaGJvYXJkXCJcclxuICAgICAgICAgICAgICAgICAgICAgICBzdHlsZT1cImJhY2tncm91bmQtY29sb3I6ICMxZTQwYWY7IGNvbG9yOiB3aGl0ZTsgcGFkZGluZzogMTJweCAyNHB4OyB0ZXh0LWRlY29yYXRpb246IG5vbmU7IGJvcmRlci1yYWRpdXM6IDZweDsgZGlzcGxheTogaW5saW5lLWJsb2NrO1wiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgUmV2aWV3IE1hbnVzY3JpcHRcclxuICAgICAgICAgICAgICAgICAgICA8L2E+XHJcbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgICAgICAgPHA+VGhhbmsgeW91IGZvciB5b3VyIGNvbnRyaWJ1dGlvbiB0byB0aGUgYWNhZGVtaWMgcmV2aWV3IHByb2Nlc3MuPC9wPlxyXG5cclxuICAgICAgICAgICAgICAgICAgPGhyIHN0eWxlPVwiYm9yZGVyOiBub25lOyBib3JkZXItdG9wOiAxcHggc29saWQgI2UyZThmMDsgbWFyZ2luOiAzMHB4IDA7XCI+XHJcbiAgICAgICAgICAgICAgICAgIDxwIHN0eWxlPVwiY29sb3I6ICM2NDc0OGI7IGZvbnQtc2l6ZTogMTRweDtcIj5cclxuICAgICAgICAgICAgICAgICAgICBCZXN0IHJlZ2FyZHMsPGJyPlxyXG4gICAgICAgICAgICAgICAgICAgIFRpbmEgRWR1Y2F0aW9uPGJyPlxyXG4gICAgICAgICAgICAgICAgICAgIDxzbWFsbD5UaGlzIGlzIGFuIGF1dG9tYXRlZCBub3RpZmljYXRpb24gZnJvbSB0aGUgZGV2ZWxvcG1lbnQgZW52aXJvbm1lbnQuPC9zbWFsbD5cclxuICAgICAgICAgICAgICAgICAgPC9wPlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgYCxcclxuICAgICAgICAgICAgfSk7XHJcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKFxyXG4gICAgICAgICAgICAgIGDinIUgRW1haWwgc2VudCBzdWNjZXNzZnVsbHkgdG8gJHtyZXZpZXdlci5lbWFpbH06YCxcclxuICAgICAgICAgICAgICBlbWFpbFJlc3VsdFxyXG4gICAgICAgICAgICApO1xyXG4gICAgICAgICAgfSBjYXRjaCAobm90aWZ5RXJyKSB7XHJcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoXHJcbiAgICAgICAgICAgICAgYOKdjCBOb3RpZmljYXRpb24vZW1haWwgZmFpbGVkIGZvciByZXZpZXdlciAke3Jldmlld2VyLmlkfSAoJHtyZXZpZXdlci5lbWFpbH0pOmAsXHJcbiAgICAgICAgICAgICAgbm90aWZ5RXJyXHJcbiAgICAgICAgICAgICk7XHJcbiAgICAgICAgICAgIC8vIExvZyBtb3JlIGRldGFpbHMgYWJvdXQgdGhlIGVycm9yXHJcbiAgICAgICAgICAgIGlmIChub3RpZnlFcnIgaW5zdGFuY2VvZiBFcnJvcikge1xyXG4gICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoYEVycm9yIG1lc3NhZ2U6ICR7bm90aWZ5RXJyLm1lc3NhZ2V9YCk7XHJcbiAgICAgICAgICAgICAgY29uc29sZS5lcnJvcihgRXJyb3Igc3RhY2s6ICR7bm90aWZ5RXJyLnN0YWNrfWApO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgfSlcclxuICAgICAgKTtcclxuICAgIH1cclxuXHJcbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oeyBzdWNjZXNzOiB0cnVlLCBtYW51c2NyaXB0IH0pO1xyXG4gIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICBjb25zb2xlLmVycm9yKFwiVW5leHBlY3RlZCBlcnJvciBpbiBtYW51c2NyaXB0IGNyZWF0aW9uOlwiLCBlcnJvcik7XHJcbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXHJcbiAgICAgIHsgZXJyb3I6IFwiSW50ZXJuYWwgU2VydmVyIEVycm9yXCIgfSxcclxuICAgICAgeyBzdGF0dXM6IDUwMCB9XHJcbiAgICApO1xyXG4gIH1cclxufVxyXG4iXSwibmFtZXMiOlsiYXV0aCIsInByaXNtYSIsIlJlc2VuZCIsIk5leHRSZXNwb25zZSIsImdlbmVyYXRlQW5kU3RvcmVQZGYiLCJyZXNlbmQiLCJwcm9jZXNzIiwiZW52IiwiUkVTRU5EX0FQSV9LRVkiLCJQT1NUIiwicmVxdWVzdCIsInNlc3Npb24iLCJ1c2VyIiwiaWQiLCJjb25zb2xlIiwibG9nIiwianNvbiIsImVycm9yIiwic3RhdHVzIiwidGl0bGUiLCJhYnN0cmFjdCIsImNvbnRlbnQiLCJrZXl3b3JkcyIsInVwbG9hZGVkRmlsZSIsInVwbG9hZGVkRmlsZU5hbWUiLCJuYW1lIiwicGRmVXJsIiwibWFudXNjcmlwdCIsImNyZWF0ZSIsImRhdGEiLCJhdXRob3JfaWQiLCJwZGZGaWxlIiwicHJpc21hRXJyb3IiLCJkZXRhaWxzIiwiRXJyb3IiLCJtZXNzYWdlIiwiU3RyaW5nIiwicmV2aWV3ZXJzIiwiZmluZE1hbnkiLCJ3aGVyZSIsInJvbGUiLCJsZW5ndGgiLCJBcnJheSIsImlzQXJyYXkiLCJQcm9taXNlIiwiYWxsIiwibWFwIiwicmV2aWV3ZXIiLCJpbmRleCIsImVtYWlsIiwicmV2aWV3IiwibWFudXNjcmlwdF9pZCIsInJldmlld2VyX2lkIiwibm90aWZpY2F0aW9uIiwiRGF0ZSIsIm5vdyIsIk1hdGgiLCJyYW5kb20iLCJ0b1N0cmluZyIsInN1YnN0cmluZyIsInVzZXJJZCIsInR5cGUiLCJyZWxhdGVkSWQiLCJlbWFpbFJlc3VsdCIsImVtYWlscyIsInNlbmQiLCJmcm9tIiwidG8iLCJzdWJqZWN0IiwiaHRtbCIsIk5FWFRBVVRIX1VSTCIsIm5vdGlmeUVyciIsInN0YWNrIiwic3VjY2VzcyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/api/manuscripts/route.ts\n");

/***/ }),

/***/ "(rsc)/./auth.ts":
/*!*****************!*\
  !*** ./auth.ts ***!
  \*****************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   auth: () => (/* binding */ auth),\n/* harmony export */   handlers: () => (/* binding */ handlers),\n/* harmony export */   signIn: () => (/* binding */ signIn),\n/* harmony export */   signOut: () => (/* binding */ signOut)\n/* harmony export */ });\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var _auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @auth/prisma-adapter */ \"(rsc)/./node_modules/@auth/prisma-adapter/index.js\");\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/prisma */ \"(rsc)/./prisma.ts\");\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n\n\n\n\n\nconst { handlers, signIn, signOut, auth } = (0,next_auth__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    adapter: (0,_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_1__.PrismaAdapter)(_prisma__WEBPACK_IMPORTED_MODULE_2__.prisma),\n    secret: process.env.AUTH_SECRET,\n    trustHost: true,\n    pages: {\n        signIn: \"/auth/signin\"\n    },\n    providers: [\n        next_auth_providers_google__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_3__[\"default\"])({\n            credentials: {\n                username: {\n                    label: \"Username\",\n                    type: \"text\",\n                    placeholder: \"Enter UserName\"\n                },\n                password: {\n                    label: \"Password\",\n                    type: \"password\",\n                    placeholder: \"Enter Password\"\n                }\n            },\n            async authorize (credentials) {\n                const { username, password } = credentials;\n                const user = {\n                    id: \"1\",\n                    name: \"Bichesq\",\n                    email: \"<EMAIL>\"\n                };\n                if (username === user.name && password === \"nextgmail.com\") {\n                    return user;\n                } else {\n                    return null;\n                }\n            }\n        })\n    ],\n    callbacks: {\n        async authorized ({ auth, request: { nextUrl } }) {\n            // This callback is required for middleware to work properly\n            const isLoggedIn = !!auth?.user;\n            const isOnDashboard = nextUrl.pathname.startsWith(\"/dashboard\");\n            const isOnManuscripts = nextUrl.pathname.startsWith(\"/manuscripts\");\n            const isOnReviews = nextUrl.pathname.startsWith(\"/reviews\");\n            console.log(\"🔐 Authorized callback - auth:\", auth);\n            console.log(\"🔐 Authorized callback - isLoggedIn:\", isLoggedIn);\n            console.log(\"🔐 Authorized callback - pathname:\", nextUrl.pathname);\n            // Allow access to protected routes only if logged in\n            if (isOnDashboard || isOnManuscripts || isOnReviews) {\n                return isLoggedIn;\n            }\n            // Allow access to all other routes\n            return true;\n        },\n        async session ({ session, token }) {\n            console.log(\"🔐 Session callback - session:\", session);\n            console.log(\"🔐 Session callback - token:\", token);\n            if (token?.sub) {\n                session.user.id = token.sub;\n                // Fetch user role from database\n                try {\n                    const user = await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.user.findUnique({\n                        where: {\n                            id: token.sub\n                        },\n                        select: {\n                            role: true\n                        }\n                    });\n                    if (user) {\n                        session.user.role = user.role;\n                    }\n                } catch (error) {\n                    console.error(\"Failed to fetch user role:\", error);\n                }\n            }\n            return session;\n        },\n        async jwt ({ token, user }) {\n            console.log(\"🔐 JWT callback - token:\", token);\n            console.log(\"🔐 JWT callback - user:\", user);\n            if (user) {\n                token.sub = user.id;\n            }\n            return token;\n        }\n    }\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./auth.ts\n");

/***/ }),

/***/ "(rsc)/./lib/pdf-service.ts":
/*!****************************!*\
  !*** ./lib/pdf-service.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   deletePdf: () => (/* binding */ deletePdf),\n/* harmony export */   generateAndStorePdf: () => (/* binding */ generateAndStorePdf)\n/* harmony export */ });\n/* harmony import */ var pdf_lib__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! pdf-lib */ \"(rsc)/./node_modules/pdf-lib/es/index.js\");\n/* harmony import */ var _vercel_blob__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @vercel/blob */ \"(rsc)/./node_modules/@vercel/blob/dist/index.js\");\n\n\nasync function generateAndStorePdf(content, title, author) {\n    try {\n        console.log(\"📄 Starting PDF generation...\");\n        // Create PDF document\n        const pdfDoc = await pdf_lib__WEBPACK_IMPORTED_MODULE_0__.PDFDocument.create();\n        let currentPage = pdfDoc.addPage();\n        const { width, height } = currentPage.getSize();\n        // Embed font\n        const font = await pdfDoc.embedFont(pdf_lib__WEBPACK_IMPORTED_MODULE_0__.StandardFonts.Helvetica);\n        const boldFont = await pdfDoc.embedFont(pdf_lib__WEBPACK_IMPORTED_MODULE_0__.StandardFonts.HelveticaBold);\n        let yPosition = height - 50;\n        const margin = 50;\n        const lineHeight = 20;\n        // Add title if provided\n        if (title) {\n            currentPage.drawText(title, {\n                x: margin,\n                y: yPosition,\n                size: 18,\n                font: boldFont,\n                color: (0,pdf_lib__WEBPACK_IMPORTED_MODULE_0__.rgb)(0, 0, 0)\n            });\n            yPosition -= 30;\n        }\n        // Add author if provided\n        if (author) {\n            currentPage.drawText(`Author: ${author}`, {\n                x: margin,\n                y: yPosition,\n                size: 12,\n                font: font,\n                color: (0,pdf_lib__WEBPACK_IMPORTED_MODULE_0__.rgb)(0.5, 0.5, 0.5)\n            });\n            yPosition -= 30;\n        }\n        // Strip HTML tags and process content more thoroughly\n        const cleanContent = content.replace(/<br\\s*\\/?>/gi, \"\\n\") // Convert <br> tags to newlines\n        .replace(/<\\/p>/gi, \"\\n\\n\") // Convert </p> tags to paragraph breaks\n        .replace(/<p[^>]*>/gi, \"\") // Remove <p> opening tags\n        .replace(/<[^>]*>/g, \"\") // Remove all other HTML tags\n        .replace(/&nbsp;/g, \" \") // Replace non-breaking spaces\n        .replace(/&amp;/g, \"&\") // Replace HTML entities\n        .replace(/&lt;/g, \"<\").replace(/&gt;/g, \">\").replace(/&quot;/g, '\"').replace(/&#39;/g, \"'\").replace(/\\s+/g, \" \") // Normalize whitespace\n        .trim();\n        // Split content into paragraphs and then words\n        const paragraphs = cleanContent.split(/\\n\\s*\\n/).filter((p)=>p.trim());\n        const maxWidth = width - margin * 2;\n        for(let i = 0; i < paragraphs.length; i++){\n            const paragraph = paragraphs[i].trim();\n            if (!paragraph) continue;\n            // Add extra space between paragraphs (except for the first one)\n            if (i > 0) {\n                yPosition -= lineHeight * 0.5;\n            }\n            const words = paragraph.split(/\\s+/);\n            let line = \"\";\n            for (const word of words){\n                const testLine = line + word + \" \";\n                const testWidth = font.widthOfTextAtSize(testLine, 12);\n                const wordWidth = font.widthOfTextAtSize(word, 12);\n                // Handle very long words that exceed maxWidth\n                if (wordWidth > maxWidth) {\n                    // Draw current line if it has content\n                    if (line.trim()) {\n                        currentPage.drawText(line.trim(), {\n                            x: margin,\n                            y: yPosition,\n                            size: 12,\n                            font: font,\n                            color: (0,pdf_lib__WEBPACK_IMPORTED_MODULE_0__.rgb)(0, 0, 0)\n                        });\n                        yPosition -= lineHeight;\n                        line = \"\";\n                        // Check if we need a new page\n                        if (yPosition < margin) {\n                            currentPage = pdfDoc.addPage();\n                            yPosition = height - margin;\n                        }\n                    }\n                    // Break the long word into smaller chunks\n                    let remainingWord = word;\n                    while(remainingWord.length > 0){\n                        let chunk = \"\";\n                        for(let i = 0; i < remainingWord.length; i++){\n                            const testChunk = chunk + remainingWord[i];\n                            if (font.widthOfTextAtSize(testChunk, 12) > maxWidth) {\n                                break;\n                            }\n                            chunk = testChunk;\n                        }\n                        if (chunk.length === 0) {\n                            chunk = remainingWord[0]; // At least take one character\n                        }\n                        currentPage.drawText(chunk, {\n                            x: margin,\n                            y: yPosition,\n                            size: 12,\n                            font: font,\n                            color: (0,pdf_lib__WEBPACK_IMPORTED_MODULE_0__.rgb)(0, 0, 0)\n                        });\n                        yPosition -= lineHeight;\n                        remainingWord = remainingWord.substring(chunk.length);\n                        // Check if we need a new page\n                        if (yPosition < margin && remainingWord.length > 0) {\n                            currentPage = pdfDoc.addPage();\n                            yPosition = height - margin;\n                        }\n                    }\n                } else if (testWidth > maxWidth && line !== \"\") {\n                    // Draw the current line\n                    currentPage.drawText(line.trim(), {\n                        x: margin,\n                        y: yPosition,\n                        size: 12,\n                        font: font,\n                        color: (0,pdf_lib__WEBPACK_IMPORTED_MODULE_0__.rgb)(0, 0, 0)\n                    });\n                    yPosition -= lineHeight;\n                    line = word + \" \";\n                    // Check if we need a new page\n                    if (yPosition < margin) {\n                        currentPage = pdfDoc.addPage();\n                        yPosition = height - margin;\n                    }\n                } else {\n                    line = testLine;\n                }\n            }\n            // Draw the last line of the paragraph\n            if (line.trim()) {\n                currentPage.drawText(line.trim(), {\n                    x: margin,\n                    y: yPosition,\n                    size: 12,\n                    font: font,\n                    color: (0,pdf_lib__WEBPACK_IMPORTED_MODULE_0__.rgb)(0, 0, 0)\n                });\n                yPosition -= lineHeight;\n                // Check if we need a new page after drawing the last line\n                if (yPosition < margin && i < paragraphs.length - 1) {\n                    currentPage = pdfDoc.addPage();\n                    yPosition = height - margin;\n                }\n            }\n        }\n        // Generate PDF bytes\n        const pdfBytes = await pdfDoc.save();\n        console.log(\"✅ PDF generated successfully\");\n        // Upload to Vercel Blob\n        const filename = `manuscript-${Date.now()}-${Math.random().toString(36).substring(7)}.pdf`;\n        console.log(`📤 Uploading PDF to Vercel Blob: ${filename}`);\n        const blob = await (0,_vercel_blob__WEBPACK_IMPORTED_MODULE_1__.put)(`manuscripts/${filename}`, Buffer.from(pdfBytes), {\n            access: \"public\",\n            contentType: \"application/pdf\"\n        });\n        console.log(`✅ PDF uploaded successfully: ${blob.url}`);\n        return blob.url;\n    } catch (error) {\n        console.error(\"❌ PDF generation/upload failed:\", error);\n        return null;\n    }\n}\nasync function deletePdf(url) {\n    try {\n        console.log(`🗑️ Deleting PDF: ${url}`);\n        await (0,_vercel_blob__WEBPACK_IMPORTED_MODULE_1__.del)(url);\n        console.log(\"✅ PDF deleted successfully\");\n        return true;\n    } catch (error) {\n        console.error(\"❌ PDF deletion failed:\", error);\n        return false;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/pdf-service.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmanuscripts%2Froute&page=%2Fapi%2Fmanuscripts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmanuscripts%2Froute.ts&appDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmanuscripts%2Froute&page=%2Fapi%2Fmanuscripts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmanuscripts%2Froute.ts&appDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_biche_Tina_Education_org_Review_Request_backend_ARRS_NextJS_tina_education_app_api_manuscripts_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/manuscripts/route.ts */ \"(rsc)/./app/api/manuscripts/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/manuscripts/route\",\n        pathname: \"/api/manuscripts\",\n        filename: \"route\",\n        bundlePath: \"app/api/manuscripts/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\api\\\\manuscripts\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_biche_Tina_Education_org_Review_Request_backend_ARRS_NextJS_tina_education_app_api_manuscripts_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZhcGklMkZtYW51c2NyaXB0cyUyRnJvdXRlJnBhZ2U9JTJGYXBpJTJGbWFudXNjcmlwdHMlMkZyb3V0ZSZhcHBQYXRocz0mcGFnZVBhdGg9cHJpdmF0ZS1uZXh0LWFwcC1kaXIlMkZhcGklMkZtYW51c2NyaXB0cyUyRnJvdXRlLnRzJmFwcERpcj1DJTNBJTVDVXNlcnMlNUNiaWNoZSU1Q1RpbmElMjBFZHVjYXRpb24ub3JnJTVDUmV2aWV3JTIwUmVxdWVzdCUyMGJhY2tlbmQlNUNBUlJTLU5leHRKUyU1Q3RpbmEtZWR1Y2F0aW9uJTVDYXBwJnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMmcm9vdERpcj1DJTNBJTVDVXNlcnMlNUNiaWNoZSU1Q1RpbmElMjBFZHVjYXRpb24ub3JnJTVDUmV2aWV3JTIwUmVxdWVzdCUyMGJhY2tlbmQlNUNBUlJTLU5leHRKUyU1Q3RpbmEtZWR1Y2F0aW9uJmlzRGV2PXRydWUmdHNjb25maWdQYXRoPXRzY29uZmlnLmpzb24mYmFzZVBhdGg9JmFzc2V0UHJlZml4PSZuZXh0Q29uZmlnT3V0cHV0PSZwcmVmZXJyZWRSZWdpb249Jm1pZGRsZXdhcmVDb25maWc9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7OztBQUErRjtBQUN2QztBQUNxQjtBQUMwRTtBQUN2SjtBQUNBO0FBQ0E7QUFDQSx3QkFBd0IseUdBQW1CO0FBQzNDO0FBQ0EsY0FBYyxrRUFBUztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsWUFBWTtBQUNaLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQSxRQUFRLHNEQUFzRDtBQUM5RDtBQUNBLFdBQVcsNEVBQVc7QUFDdEI7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUMwRjs7QUFFMUYiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBBcHBSb3V0ZVJvdXRlTW9kdWxlIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcm91dGUtbW9kdWxlcy9hcHAtcm91dGUvbW9kdWxlLmNvbXBpbGVkXCI7XG5pbXBvcnQgeyBSb3V0ZUtpbmQgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yb3V0ZS1raW5kXCI7XG5pbXBvcnQgeyBwYXRjaEZldGNoIGFzIF9wYXRjaEZldGNoIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvbGliL3BhdGNoLWZldGNoXCI7XG5pbXBvcnQgKiBhcyB1c2VybGFuZCBmcm9tIFwiQzpcXFxcVXNlcnNcXFxcYmljaGVcXFxcVGluYSBFZHVjYXRpb24ub3JnXFxcXFJldmlldyBSZXF1ZXN0IGJhY2tlbmRcXFxcQVJSUy1OZXh0SlNcXFxcdGluYS1lZHVjYXRpb25cXFxcYXBwXFxcXGFwaVxcXFxtYW51c2NyaXB0c1xcXFxyb3V0ZS50c1wiO1xuLy8gV2UgaW5qZWN0IHRoZSBuZXh0Q29uZmlnT3V0cHV0IGhlcmUgc28gdGhhdCB3ZSBjYW4gdXNlIHRoZW0gaW4gdGhlIHJvdXRlXG4vLyBtb2R1bGUuXG5jb25zdCBuZXh0Q29uZmlnT3V0cHV0ID0gXCJcIlxuY29uc3Qgcm91dGVNb2R1bGUgPSBuZXcgQXBwUm91dGVSb3V0ZU1vZHVsZSh7XG4gICAgZGVmaW5pdGlvbjoge1xuICAgICAgICBraW5kOiBSb3V0ZUtpbmQuQVBQX1JPVVRFLFxuICAgICAgICBwYWdlOiBcIi9hcGkvbWFudXNjcmlwdHMvcm91dGVcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL2FwaS9tYW51c2NyaXB0c1wiLFxuICAgICAgICBmaWxlbmFtZTogXCJyb3V0ZVwiLFxuICAgICAgICBidW5kbGVQYXRoOiBcImFwcC9hcGkvbWFudXNjcmlwdHMvcm91dGVcIlxuICAgIH0sXG4gICAgcmVzb2x2ZWRQYWdlUGF0aDogXCJDOlxcXFxVc2Vyc1xcXFxiaWNoZVxcXFxUaW5hIEVkdWNhdGlvbi5vcmdcXFxcUmV2aWV3IFJlcXVlc3QgYmFja2VuZFxcXFxBUlJTLU5leHRKU1xcXFx0aW5hLWVkdWNhdGlvblxcXFxhcHBcXFxcYXBpXFxcXG1hbnVzY3JpcHRzXFxcXHJvdXRlLnRzXCIsXG4gICAgbmV4dENvbmZpZ091dHB1dCxcbiAgICB1c2VybGFuZFxufSk7XG4vLyBQdWxsIG91dCB0aGUgZXhwb3J0cyB0aGF0IHdlIG5lZWQgdG8gZXhwb3NlIGZyb20gdGhlIG1vZHVsZS4gVGhpcyBzaG91bGRcbi8vIGJlIGVsaW1pbmF0ZWQgd2hlbiB3ZSd2ZSBtb3ZlZCB0aGUgb3RoZXIgcm91dGVzIHRvIHRoZSBuZXcgZm9ybWF0LiBUaGVzZVxuLy8gYXJlIHVzZWQgdG8gaG9vayBpbnRvIHRoZSByb3V0ZS5cbmNvbnN0IHsgd29ya0FzeW5jU3RvcmFnZSwgd29ya1VuaXRBc3luY1N0b3JhZ2UsIHNlcnZlckhvb2tzIH0gPSByb3V0ZU1vZHVsZTtcbmZ1bmN0aW9uIHBhdGNoRmV0Y2goKSB7XG4gICAgcmV0dXJuIF9wYXRjaEZldGNoKHtcbiAgICAgICAgd29ya0FzeW5jU3RvcmFnZSxcbiAgICAgICAgd29ya1VuaXRBc3luY1N0b3JhZ2VcbiAgICB9KTtcbn1cbmV4cG9ydCB7IHJvdXRlTW9kdWxlLCB3b3JrQXN5bmNTdG9yYWdlLCB3b3JrVW5pdEFzeW5jU3RvcmFnZSwgc2VydmVySG9va3MsIHBhdGNoRmV0Y2gsICB9O1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1hcHAtcm91dGUuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmanuscripts%2Froute&page=%2Fapi%2Fmanuscripts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmanuscripts%2Froute.ts&appDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./prisma.ts":
/*!*******************!*\
  !*** ./prisma.ts ***!
  \*******************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma || new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9wcmlzbWEudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQThDO0FBRTlDLE1BQU1DLGtCQUFrQkM7QUFFakIsTUFBTUMsU0FBU0YsZ0JBQWdCRSxNQUFNLElBQUksSUFBSUgsd0RBQVlBLEdBQUc7QUFFbkUsSUFBSUksSUFBcUMsRUFBRUgsZ0JBQWdCRSxNQUFNLEdBQUdBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGJpY2hlXFxUaW5hIEVkdWNhdGlvbi5vcmdcXFJldmlldyBSZXF1ZXN0IGJhY2tlbmRcXEFSUlMtTmV4dEpTXFx0aW5hLWVkdWNhdGlvblxccHJpc21hLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFByaXNtYUNsaWVudCB9IGZyb20gXCJAcHJpc21hL2NsaWVudFwiO1xyXG5cclxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHsgcHJpc21hOiBQcmlzbWFDbGllbnQgfTtcclxuXHJcbmV4cG9ydCBjb25zdCBwcmlzbWEgPSBnbG9iYWxGb3JQcmlzbWEucHJpc21hIHx8IG5ldyBQcmlzbWFDbGllbnQoKTtcclxuXHJcbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gXCJwcm9kdWN0aW9uXCIpIGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPSBwcmlzbWE7XHJcbiJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJnbG9iYWxGb3JQcmlzbWEiLCJnbG9iYWxUaGlzIiwicHJpc21hIiwicHJvY2VzcyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./prisma.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../../server/app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "async_hooks":
/*!******************************!*\
  !*** external "async_hooks" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("async_hooks");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "console":
/*!**************************!*\
  !*** external "console" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("console");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "diagnostics_channel":
/*!**************************************!*\
  !*** external "diagnostics_channel" ***!
  \**************************************/
/***/ ((module) => {

"use strict";
module.exports = require("diagnostics_channel");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "http2":
/*!************************!*\
  !*** external "http2" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("http2");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "node:events":
/*!******************************!*\
  !*** external "node:events" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:events");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:stream");

/***/ }),

/***/ "node:util":
/*!****************************!*\
  !*** external "node:util" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:util");

/***/ }),

/***/ "perf_hooks":
/*!*****************************!*\
  !*** external "perf_hooks" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("perf_hooks");

/***/ }),

/***/ "prettier/plugins/html":
/*!****************************************!*\
  !*** external "prettier/plugins/html" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = import("prettier/plugins/html");;

/***/ }),

/***/ "prettier/standalone":
/*!**************************************!*\
  !*** external "prettier/standalone" ***!
  \**************************************/
/***/ ((module) => {

"use strict";
module.exports = import("prettier/standalone");;

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "stream/web":
/*!*****************************!*\
  !*** external "stream/web" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream/web");

/***/ }),

/***/ "string_decoder":
/*!*********************************!*\
  !*** external "string_decoder" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("string_decoder");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "util/types":
/*!*****************************!*\
  !*** external "util/types" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("util/types");

/***/ }),

/***/ "worker_threads":
/*!*********************************!*\
  !*** external "worker_threads" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("worker_threads");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@auth","vendor-chunks/next-auth","vendor-chunks/oauth4webapi","vendor-chunks/jose","vendor-chunks/preact","vendor-chunks/preact-render-to-string","vendor-chunks/@panva","vendor-chunks/pdf-lib","vendor-chunks/undici","vendor-chunks/@pdf-lib","vendor-chunks/pako","vendor-chunks/@fastify","vendor-chunks/retry","vendor-chunks/@vercel","vendor-chunks/resend","vendor-chunks/is-node-process","vendor-chunks/throttleit","vendor-chunks/is-buffer","vendor-chunks/async-retry"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmanuscripts%2Froute&page=%2Fapi%2Fmanuscripts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmanuscripts%2Froute.ts&appDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();