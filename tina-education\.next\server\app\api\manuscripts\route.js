/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/manuscripts/route";
exports.ids = ["app/api/manuscripts/route"];
exports.modules = {

/***/ "(rsc)/./app/api/manuscripts/route.ts":
/*!**************************************!*\
  !*** ./app/api/manuscripts/route.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var _auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/auth */ \"(rsc)/./auth.ts\");\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../prisma */ \"(rsc)/./prisma.ts\");\n/* harmony import */ var resend__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! resend */ \"(rsc)/./node_modules/resend/dist/index.mjs\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_pdf_service__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../lib/pdf-service */ \"(rsc)/./lib/pdf-service.ts\");\n\n\n\n\n\nconst resend = new resend__WEBPACK_IMPORTED_MODULE_2__.Resend(process.env.RESEND_API_KEY);\nasync function POST(request) {\n    const session = await (0,_auth__WEBPACK_IMPORTED_MODULE_0__.auth)();\n    if (!session?.user?.id) return console.log(\"Unauthorized\"), next_server__WEBPACK_IMPORTED_MODULE_3__.NextResponse.json({\n        error: \"Unauthorized\"\n    }, {\n        status: 401\n    });\n    try {\n        const { title, abstract, content, keywords, uploadedFile, uploadedFileName } = await request.json();\n        console.log(\"📄 Starting manuscript submission process...\");\n        console.log(`📝 Title: ${title}`);\n        console.log(`👤 Author: ${session.user?.name}`);\n        // Generate and upload PDF to Vercel Blob\n        const pdfUrl = await (0,_lib_pdf_service__WEBPACK_IMPORTED_MODULE_4__.generateAndStorePdf)(content, title, session.user?.name || \"Unknown Author\");\n        if (!pdfUrl) {\n            console.log(\"❌ Failed to generate PDF\");\n            return next_server__WEBPACK_IMPORTED_MODULE_3__.NextResponse.json({\n                error: \"Failed to generate PDF\"\n            }, {\n                status: 500\n            });\n        }\n        // Create manuscript record with error logging\n        let manuscript;\n        try {\n            manuscript = await _prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.manuscript.create({\n                data: {\n                    author_id: session.user.id,\n                    title,\n                    abstract,\n                    content,\n                    keywords,\n                    pdfFile: pdfUrl,\n                    uploadedFile: uploadedFile || null,\n                    uploadedFileName: uploadedFileName || null\n                }\n            });\n        } catch (prismaError) {\n            console.error(\"Prisma manuscript.create failed:\", prismaError, {\n                author_id: session.user.id,\n                title,\n                abstract,\n                content,\n                keywords,\n                pdfFile: pdfUrl,\n                uploadedFile: uploadedFile || null,\n                uploadedFileName: uploadedFileName || null\n            });\n            return next_server__WEBPACK_IMPORTED_MODULE_3__.NextResponse.json({\n                error: \"Failed to create manuscript\",\n                details: prismaError instanceof Error ? prismaError.message : String(prismaError)\n            }, {\n                status: 500\n            });\n        }\n        // Get reviewers\n        const reviewers = await _prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.user.findMany({\n            where: {\n                role: \"REVIEWER\"\n            }\n        });\n        // Send notifications\n        console.log(`📧 Starting email notifications for ${reviewers.length} reviewers`);\n        console.log(`🔑 Resend API Key configured: ${process.env.RESEND_API_KEY ? \"Yes\" : \"No\"}`);\n        if (!Array.isArray(reviewers) || reviewers.length === 0) {\n            console.log(\"⚠️ No reviewers found. Skipping notifications.\");\n        } else {\n            await Promise.all(reviewers.map(async (reviewer, index)=>{\n                try {\n                    console.log(`📝 Processing reviewer ${index + 1}/${reviewers.length}: ${reviewer.email}`);\n                    // Create review assignment\n                    const review = await _prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.review.create({\n                        data: {\n                            manuscript_id: manuscript.id,\n                            reviewer_id: reviewer.id,\n                            content: `Review assignment for manuscript: ${manuscript.title}`,\n                            status: \"PENDING\"\n                        }\n                    });\n                    console.log(`✅ Review assignment created for ${reviewer.email}:`, review.id);\n                    // Create dashboard notification\n                    const notification = await _prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.notification.create({\n                        data: {\n                            id: `notif_${Date.now()}_${Math.random().toString(36).substring(7)}`,\n                            userId: reviewer.id,\n                            title: \"New Manuscript Submitted\",\n                            message: `New manuscript \"${manuscript.title}\" from ${session.user?.name} requires your review`,\n                            type: \"MANUSCRIPT_SUBMISSION\",\n                            relatedId: manuscript.id\n                        }\n                    });\n                    console.log(`✅ Dashboard notification created for ${reviewer.email}:`, notification.id);\n                    // Send email notification\n                    const emailResult = await resend.emails.send({\n                        from: \"Tina Education <<EMAIL>>\",\n                        to: reviewer.email,\n                        subject: `New Manuscript Review Request: \"${manuscript.title}\"`,\n                        html: `\n                <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n                  <div style=\"background-color: #f8fafc; padding: 20px; border-radius: 8px; margin-bottom: 20px;\">\n                    <h2 style=\"color: #1e40af; margin: 0;\">📝 New Manuscript Review Request</h2>\n                  </div>\n\n                  <p>Hello <strong>${reviewer.name || \"Reviewer\"}</strong>,</p>\n\n                  <p>A new manuscript has been submitted and requires your expert review:</p>\n\n                  <div style=\"background-color: #f1f5f9; padding: 15px; border-radius: 6px; margin: 20px 0;\">\n                    <h3 style=\"margin: 0 0 10px 0; color: #334155;\">📄 Manuscript Details</h3>\n                    <p style=\"margin: 5px 0;\"><strong>Title:</strong> ${manuscript.title}</p>\n                    <p style=\"margin: 5px 0;\"><strong>Author:</strong> ${session.user?.name}</p>\n                    <p style=\"margin: 5px 0;\"><strong>Abstract:</strong> ${manuscript.abstract}</p>\n                    <p style=\"margin: 5px 0;\"><strong>Keywords:</strong> ${manuscript.keywords}</p>\n                  </div>\n\n                  <p>Please log in to the Tina Education system to access the full manuscript and begin your review.</p>\n\n                  <div style=\"text-align: center; margin: 30px 0;\">\n                    <a href=\"${process.env.NEXTAUTH_URL || \"http://localhost:3000\"}/dashboard\"\n                       style=\"background-color: #1e40af; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;\">\n                      Review Manuscript\n                    </a>\n                  </div>\n\n                  <p>Thank you for your contribution to the academic review process.</p>\n\n                  <hr style=\"border: none; border-top: 1px solid #e2e8f0; margin: 30px 0;\">\n                  <p style=\"color: #64748b; font-size: 14px;\">\n                    Best regards,<br>\n                    Tina Education<br>\n                    <small>This is an automated notification from the development environment.</small>\n                  </p>\n                </div>\n              `\n                    });\n                    console.log(`✅ Email sent successfully to ${reviewer.email}:`, emailResult);\n                } catch (notifyErr) {\n                    console.error(`❌ Notification/email failed for reviewer ${reviewer.id} (${reviewer.email}):`, notifyErr);\n                    // Log more details about the error\n                    if (notifyErr instanceof Error) {\n                        console.error(`Error message: ${notifyErr.message}`);\n                        console.error(`Error stack: ${notifyErr.stack}`);\n                    }\n                }\n            }));\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_3__.NextResponse.json({\n            success: true,\n            manuscript\n        });\n    } catch (error) {\n        console.error(\"Unexpected error in manuscript creation:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_3__.NextResponse.json({\n            error: \"Internal Server Error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/manuscripts/route.ts\n");

/***/ }),

/***/ "(rsc)/./auth.ts":
/*!*****************!*\
  !*** ./auth.ts ***!
  \*****************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   auth: () => (/* binding */ auth),\n/* harmony export */   handlers: () => (/* binding */ handlers),\n/* harmony export */   signIn: () => (/* binding */ signIn),\n/* harmony export */   signOut: () => (/* binding */ signOut)\n/* harmony export */ });\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var _auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @auth/prisma-adapter */ \"(rsc)/./node_modules/@auth/prisma-adapter/index.js\");\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/prisma */ \"(rsc)/./prisma.ts\");\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n\n\n\n\n\nconst { handlers, signIn, signOut, auth } = (0,next_auth__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    adapter: (0,_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_1__.PrismaAdapter)(_prisma__WEBPACK_IMPORTED_MODULE_2__.prisma),\n    secret: process.env.AUTH_SECRET,\n    trustHost: true,\n    pages: {\n        signIn: \"/auth/signin\"\n    },\n    providers: [\n        next_auth_providers_google__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_3__[\"default\"])({\n            credentials: {\n                username: {\n                    label: \"Username\",\n                    type: \"text\",\n                    placeholder: \"Enter UserName\"\n                },\n                password: {\n                    label: \"Password\",\n                    type: \"password\",\n                    placeholder: \"Enter Password\"\n                }\n            },\n            async authorize (credentials) {\n                const { username, password } = credentials;\n                const user = {\n                    id: \"1\",\n                    name: \"Bichesq\",\n                    email: \"<EMAIL>\"\n                };\n                if (username === user.name && password === \"nextgmail.com\") {\n                    return user;\n                } else {\n                    return null;\n                }\n            }\n        })\n    ],\n    callbacks: {\n        async authorized ({ auth, request: { nextUrl } }) {\n            // This callback is required for middleware to work properly\n            const isLoggedIn = !!auth?.user;\n            const isOnDashboard = nextUrl.pathname.startsWith(\"/dashboard\");\n            const isOnManuscripts = nextUrl.pathname.startsWith(\"/manuscripts\");\n            const isOnReviews = nextUrl.pathname.startsWith(\"/reviews\");\n            console.log(\"🔐 Authorized callback - auth:\", auth);\n            console.log(\"🔐 Authorized callback - isLoggedIn:\", isLoggedIn);\n            console.log(\"🔐 Authorized callback - pathname:\", nextUrl.pathname);\n            // Allow access to protected routes only if logged in\n            if (isOnDashboard || isOnManuscripts || isOnReviews) {\n                return isLoggedIn;\n            }\n            // Allow access to all other routes\n            return true;\n        },\n        async session ({ session, token }) {\n            console.log(\"🔐 Session callback - session:\", session);\n            console.log(\"🔐 Session callback - token:\", token);\n            if (token?.sub) {\n                session.user.id = token.sub;\n                // Fetch user role from database\n                try {\n                    const user = await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.user.findUnique({\n                        where: {\n                            id: token.sub\n                        },\n                        select: {\n                            role: true\n                        }\n                    });\n                    if (user) {\n                        session.user.role = user.role;\n                    }\n                } catch (error) {\n                    console.error(\"Failed to fetch user role:\", error);\n                }\n            }\n            return session;\n        },\n        async jwt ({ token, user }) {\n            console.log(\"🔐 JWT callback - token:\", token);\n            console.log(\"🔐 JWT callback - user:\", user);\n            if (user) {\n                token.sub = user.id;\n            }\n            return token;\n        }\n    }\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./auth.ts\n");

/***/ }),

/***/ "(rsc)/./lib/pdf-service.ts":
/*!****************************!*\
  !*** ./lib/pdf-service.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   deletePdf: () => (/* binding */ deletePdf),\n/* harmony export */   generateAndStorePdf: () => (/* binding */ generateAndStorePdf)\n/* harmony export */ });\n/* harmony import */ var pdf_lib__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! pdf-lib */ \"(rsc)/./node_modules/pdf-lib/es/index.js\");\n/* harmony import */ var _vercel_blob__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @vercel/blob */ \"(rsc)/./node_modules/@vercel/blob/dist/index.js\");\n\n\nasync function generateAndStorePdf(content, title, author) {\n    try {\n        console.log(\"📄 Starting PDF generation...\");\n        // Create PDF document\n        const pdfDoc = await pdf_lib__WEBPACK_IMPORTED_MODULE_0__.PDFDocument.create();\n        let currentPage = pdfDoc.addPage();\n        const { width, height } = currentPage.getSize();\n        // Embed fonts\n        const fonts = {\n            regular: await pdfDoc.embedFont(pdf_lib__WEBPACK_IMPORTED_MODULE_0__.StandardFonts.Helvetica),\n            bold: await pdfDoc.embedFont(pdf_lib__WEBPACK_IMPORTED_MODULE_0__.StandardFonts.HelveticaBold),\n            italic: await pdfDoc.embedFont(pdf_lib__WEBPACK_IMPORTED_MODULE_0__.StandardFonts.HelveticaOblique),\n            boldItalic: await pdfDoc.embedFont(pdf_lib__WEBPACK_IMPORTED_MODULE_0__.StandardFonts.HelveticaBoldOblique)\n        };\n        const margin = 50;\n        const maxWidth = width - margin * 2;\n        const lineHeight = 16;\n        let yPosition = height - 50;\n        // Initialize render context\n        const context = {\n            page: currentPage,\n            yPosition,\n            fonts,\n            margin,\n            maxWidth,\n            lineHeight,\n            pdfDoc,\n            pageWidth: width,\n            pageHeight: height\n        };\n        // Add title if provided\n        if (title) {\n            context.yPosition = drawText(context, title, {\n                font: fonts.bold,\n                size: 20,\n                color: (0,pdf_lib__WEBPACK_IMPORTED_MODULE_0__.rgb)(0, 0, 0)\n            });\n            context.yPosition -= 20;\n        }\n        // Add author if provided\n        if (author) {\n            context.yPosition = drawText(context, `Author: ${author}`, {\n                font: fonts.regular,\n                size: 12,\n                color: (0,pdf_lib__WEBPACK_IMPORTED_MODULE_0__.rgb)(0.5, 0.5, 0.5)\n            });\n            context.yPosition -= 20;\n        }\n        // Parse HTML content into structured elements\n        const elements = parseHtmlContent(content);\n        // Render all elements\n        for (const element of elements){\n            context.yPosition = renderElement(context, element);\n        }\n        // Update the current page reference\n        currentPage = context.page;\n        // Generate PDF bytes\n        const pdfBytes = await pdfDoc.save();\n        console.log(\"✅ PDF generated successfully\");\n        // Upload to Vercel Blob\n        const filename = `manuscript-${Date.now()}-${Math.random().toString(36).substring(7)}.pdf`;\n        console.log(`📤 Uploading PDF to Vercel Blob: ${filename}`);\n        const blob = await (0,_vercel_blob__WEBPACK_IMPORTED_MODULE_1__.put)(`manuscripts/${filename}`, Buffer.from(pdfBytes), {\n            access: \"public\",\n            contentType: \"application/pdf\"\n        });\n        console.log(`✅ PDF uploaded successfully: ${blob.url}`);\n        return blob.url;\n    } catch (error) {\n        console.error(\"❌ PDF generation/upload failed:\", error);\n        return null;\n    }\n}\n// Helper function to parse HTML content into structured elements\nfunction parseHtmlContent(html) {\n    const elements = [];\n    // Clean up HTML entities first\n    const cleanHtml = html.replace(/&nbsp;/g, \" \").replace(/&amp;/g, \"&\").replace(/&lt;/g, \"<\").replace(/&gt;/g, \">\").replace(/&quot;/g, '\"').replace(/&#39;/g, \"'\");\n    // First, handle inline formatting (bold, italic) by converting to markers\n    let processedHtml = cleanHtml.replace(/<(strong|b)([^>]*)>(.*?)<\\/(strong|b)>/gi, \"**BOLD_START**$3**BOLD_END**\").replace(/<(em|i)([^>]*)>(.*?)<\\/(em|i)>/gi, \"**ITALIC_START**$3**ITALIC_END**\");\n    // Parse different HTML elements\n    const htmlParts = processedHtml.split(/(<\\/?(?:h[1-6]|p|ul|ol|li|br)[^>]*>)/gi);\n    let currentElement = null;\n    let listLevel = 0;\n    let inList = false;\n    for(let i = 0; i < htmlParts.length; i++){\n        const part = htmlParts[i].trim();\n        if (!part) continue;\n        // Check if it's an HTML tag\n        if (part.startsWith(\"<\")) {\n            const tagMatch = part.match(/<\\/?([a-z0-9]+)/i);\n            if (!tagMatch) continue;\n            const tag = tagMatch[1].toLowerCase();\n            const isClosing = part.startsWith(\"</\");\n            if (tag.match(/^h[1-6]$/)) {\n                if (!isClosing) {\n                    const level = parseInt(tag.charAt(1));\n                    currentElement = {\n                        type: \"heading\",\n                        content: \"\",\n                        level\n                    };\n                } else if (currentElement) {\n                    currentElement.content = processInlineFormatting(currentElement.content);\n                    elements.push(currentElement);\n                    currentElement = null;\n                }\n            } else if (tag === \"p\") {\n                if (!isClosing) {\n                    currentElement = {\n                        type: \"paragraph\",\n                        content: \"\"\n                    };\n                } else if (currentElement) {\n                    currentElement.content = processInlineFormatting(currentElement.content);\n                    elements.push(currentElement);\n                    currentElement = null;\n                }\n            } else if (tag === \"br\") {\n                if (currentElement) {\n                    currentElement.content += \"\\n\";\n                } else {\n                    elements.push({\n                        type: \"text\",\n                        content: \"\\n\"\n                    });\n                }\n            } else if (tag === \"ul\" || tag === \"ol\") {\n                if (!isClosing) {\n                    inList = true;\n                    listLevel++;\n                } else {\n                    inList = false;\n                    listLevel = Math.max(0, listLevel - 1);\n                }\n            } else if (tag === \"li\") {\n                if (!isClosing) {\n                    currentElement = {\n                        type: \"list-item\",\n                        content: \"\",\n                        level: listLevel\n                    };\n                } else if (currentElement) {\n                    currentElement.content = processInlineFormatting(currentElement.content);\n                    elements.push(currentElement);\n                    currentElement = null;\n                }\n            }\n        } else {\n            // It's text content\n            if (currentElement) {\n                currentElement.content += part;\n            } else {\n                // Text outside of any element, treat as paragraph\n                const processedContent = processInlineFormatting(part);\n                elements.push({\n                    type: \"paragraph\",\n                    content: processedContent\n                });\n            }\n        }\n    }\n    // Add any remaining element\n    if (currentElement) {\n        currentElement.content = processInlineFormatting(currentElement.content);\n        elements.push(currentElement);\n    }\n    return elements.filter((el)=>el.content.trim().length > 0);\n}\n// Helper function to process inline formatting markers\nfunction processInlineFormatting(text) {\n    // For now, we'll strip the markers and return clean text\n    // In a more advanced implementation, we could parse these into text segments with formatting\n    return text.replace(/\\*\\*BOLD_START\\*\\*/g, \"\").replace(/\\*\\*BOLD_END\\*\\*/g, \"\").replace(/\\*\\*ITALIC_START\\*\\*/g, \"\").replace(/\\*\\*ITALIC_END\\*\\*/g, \"\");\n}\n// Helper function to render a single element\nfunction renderElement(context, element) {\n    switch(element.type){\n        case \"heading\":\n            return renderHeading(context, element);\n        case \"paragraph\":\n            return renderParagraph(context, element);\n        case \"list-item\":\n            return renderListItem(context, element);\n        default:\n            return renderParagraph(context, element);\n    }\n}\n// Helper function to render headings\nfunction renderHeading(context, element) {\n    const level = element.level || 1;\n    const sizes = [\n        18,\n        16,\n        14,\n        13,\n        12,\n        11\n    ]; // Font sizes for h1-h6\n    const size = sizes[Math.min(level - 1, 5)];\n    // Add extra space before heading (except at top of page)\n    if (context.yPosition < context.pageHeight - 100) {\n        context.yPosition -= context.lineHeight;\n    }\n    context.yPosition = drawText(context, element.content, {\n        font: context.fonts.bold,\n        size,\n        color: (0,pdf_lib__WEBPACK_IMPORTED_MODULE_0__.rgb)(0, 0, 0)\n    });\n    // Add space after heading\n    context.yPosition -= context.lineHeight * 0.5;\n    return context.yPosition;\n}\n// Helper function to render paragraphs\nfunction renderParagraph(context, element) {\n    // Add space before paragraph\n    context.yPosition -= context.lineHeight * 0.3;\n    context.yPosition = drawText(context, element.content, {\n        font: context.fonts.regular,\n        size: 12,\n        color: (0,pdf_lib__WEBPACK_IMPORTED_MODULE_0__.rgb)(0, 0, 0)\n    });\n    // Add space after paragraph\n    context.yPosition -= context.lineHeight * 0.7;\n    return context.yPosition;\n}\n// Helper function to render list items\nfunction renderListItem(context, element) {\n    const indent = (element.level || 1) * 20;\n    const bullet = \"• \";\n    // Draw bullet\n    context.page.drawText(bullet, {\n        x: context.margin + indent,\n        y: context.yPosition,\n        size: 12,\n        font: context.fonts.regular,\n        color: (0,pdf_lib__WEBPACK_IMPORTED_MODULE_0__.rgb)(0, 0, 0)\n    });\n    // Draw text with proper indentation\n    const bulletWidth = context.fonts.regular.widthOfTextAtSize(bullet, 12);\n    const textX = context.margin + indent + bulletWidth;\n    const availableWidth = context.maxWidth - indent - bulletWidth;\n    context.yPosition = drawWrappedText(context, element.content, {\n        font: context.fonts.regular,\n        size: 12,\n        color: (0,pdf_lib__WEBPACK_IMPORTED_MODULE_0__.rgb)(0, 0, 0),\n        x: textX,\n        maxWidth: availableWidth\n    });\n    // Add space after list item\n    context.yPosition -= context.lineHeight * 0.3;\n    return context.yPosition;\n}\n// Helper function to draw text with automatic wrapping\nfunction drawText(context, text, options) {\n    const x = options.x || context.margin;\n    const maxWidth = options.maxWidth || context.maxWidth;\n    return drawWrappedText(context, text, {\n        ...options,\n        x,\n        maxWidth\n    });\n}\n// Helper function to draw wrapped text\nfunction drawWrappedText(context, text, options) {\n    const words = text.trim().split(/\\s+/);\n    let line = \"\";\n    let yPosition = context.yPosition;\n    for (const word of words){\n        const testLine = line + word + \" \";\n        const testWidth = options.font.widthOfTextAtSize(testLine, options.size);\n        const wordWidth = options.font.widthOfTextAtSize(word, options.size);\n        // Handle very long words that exceed maxWidth\n        if (wordWidth > options.maxWidth) {\n            // Draw current line if it has content\n            if (line.trim()) {\n                context.page.drawText(line.trim(), {\n                    x: options.x,\n                    y: yPosition,\n                    size: options.size,\n                    font: options.font,\n                    color: options.color\n                });\n                yPosition -= context.lineHeight;\n                line = \"\";\n                // Check if we need a new page\n                if (yPosition < context.margin) {\n                    context.page = context.pdfDoc.addPage();\n                    yPosition = context.pageHeight - context.margin;\n                }\n            }\n            // Break the long word into smaller chunks\n            let remainingWord = word;\n            while(remainingWord.length > 0){\n                let chunk = \"\";\n                for(let i = 0; i < remainingWord.length; i++){\n                    const testChunk = chunk + remainingWord[i];\n                    if (options.font.widthOfTextAtSize(testChunk, options.size) > options.maxWidth) {\n                        break;\n                    }\n                    chunk = testChunk;\n                }\n                if (chunk.length === 0) {\n                    chunk = remainingWord[0]; // At least take one character\n                }\n                context.page.drawText(chunk, {\n                    x: options.x,\n                    y: yPosition,\n                    size: options.size,\n                    font: options.font,\n                    color: options.color\n                });\n                yPosition -= context.lineHeight;\n                remainingWord = remainingWord.substring(chunk.length);\n                // Check if we need a new page\n                if (yPosition < context.margin && remainingWord.length > 0) {\n                    context.page = context.pdfDoc.addPage();\n                    yPosition = context.pageHeight - context.margin;\n                }\n            }\n        } else if (testWidth > options.maxWidth && line !== \"\") {\n            // Draw the current line\n            context.page.drawText(line.trim(), {\n                x: options.x,\n                y: yPosition,\n                size: options.size,\n                font: options.font,\n                color: options.color\n            });\n            yPosition -= context.lineHeight;\n            line = word + \" \";\n            // Check if we need a new page\n            if (yPosition < context.margin) {\n                context.page = context.pdfDoc.addPage();\n                yPosition = context.pageHeight - context.margin;\n            }\n        } else {\n            line = testLine;\n        }\n    }\n    // Draw the last line\n    if (line.trim()) {\n        context.page.drawText(line.trim(), {\n            x: options.x,\n            y: yPosition,\n            size: options.size,\n            font: options.font,\n            color: options.color\n        });\n        yPosition -= context.lineHeight;\n        // Check if we need a new page\n        if (yPosition < context.margin) {\n            context.page = context.pdfDoc.addPage();\n            yPosition = context.pageHeight - context.margin;\n        }\n    }\n    return yPosition;\n}\nasync function deletePdf(url) {\n    try {\n        console.log(`🗑️ Deleting PDF: ${url}`);\n        await (0,_vercel_blob__WEBPACK_IMPORTED_MODULE_1__.del)(url);\n        console.log(\"✅ PDF deleted successfully\");\n        return true;\n    } catch (error) {\n        console.error(\"❌ PDF deletion failed:\", error);\n        return false;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/pdf-service.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmanuscripts%2Froute&page=%2Fapi%2Fmanuscripts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmanuscripts%2Froute.ts&appDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmanuscripts%2Froute&page=%2Fapi%2Fmanuscripts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmanuscripts%2Froute.ts&appDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_biche_Tina_Education_org_Review_Request_backend_ARRS_NextJS_tina_education_app_api_manuscripts_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/manuscripts/route.ts */ \"(rsc)/./app/api/manuscripts/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/manuscripts/route\",\n        pathname: \"/api/manuscripts\",\n        filename: \"route\",\n        bundlePath: \"app/api/manuscripts/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\api\\\\manuscripts\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_biche_Tina_Education_org_Review_Request_backend_ARRS_NextJS_tina_education_app_api_manuscripts_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmanuscripts%2Froute&page=%2Fapi%2Fmanuscripts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmanuscripts%2Froute.ts&appDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./prisma.ts":
/*!*******************!*\
  !*** ./prisma.ts ***!
  \*******************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma || new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9wcmlzbWEudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQThDO0FBRTlDLE1BQU1DLGtCQUFrQkM7QUFFakIsTUFBTUMsU0FBU0YsZ0JBQWdCRSxNQUFNLElBQUksSUFBSUgsd0RBQVlBLEdBQUc7QUFFbkUsSUFBSUksSUFBcUMsRUFBRUgsZ0JBQWdCRSxNQUFNLEdBQUdBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGJpY2hlXFxUaW5hIEVkdWNhdGlvbi5vcmdcXFJldmlldyBSZXF1ZXN0IGJhY2tlbmRcXEFSUlMtTmV4dEpTXFx0aW5hLWVkdWNhdGlvblxccHJpc21hLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFByaXNtYUNsaWVudCB9IGZyb20gXCJAcHJpc21hL2NsaWVudFwiO1xyXG5cclxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHsgcHJpc21hOiBQcmlzbWFDbGllbnQgfTtcclxuXHJcbmV4cG9ydCBjb25zdCBwcmlzbWEgPSBnbG9iYWxGb3JQcmlzbWEucHJpc21hIHx8IG5ldyBQcmlzbWFDbGllbnQoKTtcclxuXHJcbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gXCJwcm9kdWN0aW9uXCIpIGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPSBwcmlzbWE7XHJcbiJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJnbG9iYWxGb3JQcmlzbWEiLCJnbG9iYWxUaGlzIiwicHJpc21hIiwicHJvY2VzcyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./prisma.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../../server/app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "async_hooks":
/*!******************************!*\
  !*** external "async_hooks" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("async_hooks");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "console":
/*!**************************!*\
  !*** external "console" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("console");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "diagnostics_channel":
/*!**************************************!*\
  !*** external "diagnostics_channel" ***!
  \**************************************/
/***/ ((module) => {

"use strict";
module.exports = require("diagnostics_channel");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "http2":
/*!************************!*\
  !*** external "http2" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("http2");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "node:events":
/*!******************************!*\
  !*** external "node:events" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:events");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:stream");

/***/ }),

/***/ "node:util":
/*!****************************!*\
  !*** external "node:util" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:util");

/***/ }),

/***/ "perf_hooks":
/*!*****************************!*\
  !*** external "perf_hooks" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("perf_hooks");

/***/ }),

/***/ "prettier/plugins/html":
/*!****************************************!*\
  !*** external "prettier/plugins/html" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = import("prettier/plugins/html");;

/***/ }),

/***/ "prettier/standalone":
/*!**************************************!*\
  !*** external "prettier/standalone" ***!
  \**************************************/
/***/ ((module) => {

"use strict";
module.exports = import("prettier/standalone");;

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "stream/web":
/*!*****************************!*\
  !*** external "stream/web" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream/web");

/***/ }),

/***/ "string_decoder":
/*!*********************************!*\
  !*** external "string_decoder" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("string_decoder");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "util/types":
/*!*****************************!*\
  !*** external "util/types" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("util/types");

/***/ }),

/***/ "worker_threads":
/*!*********************************!*\
  !*** external "worker_threads" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("worker_threads");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@auth","vendor-chunks/next-auth","vendor-chunks/oauth4webapi","vendor-chunks/jose","vendor-chunks/preact","vendor-chunks/preact-render-to-string","vendor-chunks/@panva","vendor-chunks/pdf-lib","vendor-chunks/undici","vendor-chunks/@pdf-lib","vendor-chunks/pako","vendor-chunks/@fastify","vendor-chunks/retry","vendor-chunks/@vercel","vendor-chunks/resend","vendor-chunks/is-node-process","vendor-chunks/throttleit","vendor-chunks/is-buffer","vendor-chunks/async-retry"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmanuscripts%2Froute&page=%2Fapi%2Fmanuscripts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmanuscripts%2Froute.ts&appDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();