"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/reviews/[id]/review/page",{

/***/ "(app-pages-browser)/./app/components/reviews/ReviewForm.tsx":
/*!***********************************************!*\
  !*** ./app/components/reviews/ReviewForm.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ReviewForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction ReviewForm(param) {\n    let { reviewData, setReviewData } = param;\n    _s();\n    const [activeSection, setActiveSection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"evaluation\");\n    const handleInputChange = (field, value)=>{\n        setReviewData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n    };\n    const handleRatingClick = (rating)=>{\n        handleInputChange(\"overallRating\", rating);\n    };\n    const recommendations = [\n        {\n            value: \"ACCEPT\",\n            label: \"Accept\",\n            color: \"bg-green-100 text-green-800 border-green-200\"\n        },\n        {\n            value: \"MINOR_REVISIONS\",\n            label: \"Minor Revisions\",\n            color: \"bg-yellow-100 text-yellow-800 border-yellow-200\"\n        },\n        {\n            value: \"MAJOR_REVISIONS\",\n            label: \"Major Revisions\",\n            color: \"bg-orange-100 text-orange-800 border-orange-200\"\n        },\n        {\n            value: \"REJECT\",\n            label: \"Reject\",\n            color: \"bg-red-100 text-red-800 border-red-200\"\n        }\n    ];\n    const sections = [\n        {\n            id: \"evaluation\",\n            label: \"Evaluation\",\n            icon: \"📋\"\n        },\n        {\n            id: \"feedback\",\n            label: \"Feedback\",\n            icon: \"💭\"\n        },\n        {\n            id: \"recommendation\",\n            label: \"Recommendation\",\n            icon: \"⭐\"\n        },\n        {\n            id: \"comments\",\n            label: \"Comments\",\n            icon: \"💬\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full flex\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-64 bg-gray-50 border-r border-gray-200 p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                        children: \"Review Sections\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\reviews\\\\ReviewForm.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"space-y-2\",\n                        children: sections.map((section)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setActiveSection(section.id),\n                                className: \"w-full text-left px-4 py-3 rounded-lg transition-colors \".concat(activeSection === section.id ? \"bg-blue-100 text-blue-900 border border-blue-200\" : \"text-gray-700 hover:bg-gray-100\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"mr-2\",\n                                        children: section.icon\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\reviews\\\\ReviewForm.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 15\n                                    }, this),\n                                    section.label\n                                ]\n                            }, section.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\reviews\\\\ReviewForm.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\reviews\\\\ReviewForm.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-8 p-4 bg-white rounded-lg border border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-sm font-medium text-gray-900 mb-2\",\n                                children: \"Completion Status\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\reviews\\\\ReviewForm.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2 text-xs\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center \".concat(reviewData.contentEvaluation ? \"text-green-600\" : \"text-gray-400\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"mr-2\",\n                                                children: reviewData.contentEvaluation ? \"✅\" : \"⭕\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\reviews\\\\ReviewForm.tsx\",\n                                                lineNumber: 109,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Content Evaluation\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\reviews\\\\ReviewForm.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center \".concat(reviewData.styleEvaluation ? \"text-green-600\" : \"text-gray-400\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"mr-2\",\n                                                children: reviewData.styleEvaluation ? \"✅\" : \"⭕\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\reviews\\\\ReviewForm.tsx\",\n                                                lineNumber: 117,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Style Evaluation\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\reviews\\\\ReviewForm.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center \".concat(reviewData.strengths ? \"text-green-600\" : \"text-gray-400\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"mr-2\",\n                                                children: reviewData.strengths ? \"✅\" : \"⭕\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\reviews\\\\ReviewForm.tsx\",\n                                                lineNumber: 125,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Strengths\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\reviews\\\\ReviewForm.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center \".concat(reviewData.weaknesses ? \"text-green-600\" : \"text-gray-400\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"mr-2\",\n                                                children: reviewData.weaknesses ? \"✅\" : \"⭕\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\reviews\\\\ReviewForm.tsx\",\n                                                lineNumber: 131,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Areas for Improvement\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\reviews\\\\ReviewForm.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center \".concat(reviewData.recommendation ? \"text-green-600\" : \"text-gray-400\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"mr-2\",\n                                                children: reviewData.recommendation ? \"✅\" : \"⭕\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\reviews\\\\ReviewForm.tsx\",\n                                                lineNumber: 139,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Recommendation\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\reviews\\\\ReviewForm.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center \".concat(reviewData.publicComments ? \"text-green-600\" : \"text-gray-400\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"mr-2\",\n                                                children: reviewData.publicComments ? \"✅\" : \"⭕\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\reviews\\\\ReviewForm.tsx\",\n                                                lineNumber: 147,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Public Comments\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\reviews\\\\ReviewForm.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\reviews\\\\ReviewForm.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\reviews\\\\ReviewForm.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\reviews\\\\ReviewForm.tsx\",\n                lineNumber: 79,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 p-6 overflow-auto\",\n                children: [\n                    activeSection === \"evaluation\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-4xl\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-gray-900 mb-6\",\n                                children: \"Manuscript Evaluation\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\reviews\\\\ReviewForm.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-8 p-6 bg-white rounded-lg border border-gray-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                        children: \"Overall Rating\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\reviews\\\\ReviewForm.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 mb-2\",\n                                        children: [\n                                            [\n                                                1,\n                                                2,\n                                                3,\n                                                4,\n                                                5\n                                            ].map((star)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleRatingClick(star),\n                                                    className: \"text-3xl transition-colors \".concat(star <= reviewData.overallRating ? \"text-yellow-400 hover:text-yellow-500\" : \"text-gray-300 hover:text-gray-400\"),\n                                                    children: \"★\"\n                                                }, star, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\reviews\\\\ReviewForm.tsx\",\n                                                    lineNumber: 171,\n                                                    columnNumber: 19\n                                                }, this)),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-4 text-sm text-gray-600\",\n                                                children: [\n                                                    reviewData.overallRating,\n                                                    \"/5 stars\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\reviews\\\\ReviewForm.tsx\",\n                                                lineNumber: 183,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\reviews\\\\ReviewForm.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: \"Rate the overall quality of this manuscript (1 = Poor, 5 = Excellent)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\reviews\\\\ReviewForm.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\reviews\\\\ReviewForm.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-lg font-semibold text-gray-900 mb-3\",\n                                        children: \"Content Evaluation\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\reviews\\\\ReviewForm.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 mb-4\",\n                                        children: \"Evaluate the accuracy, relevance, depth, and scholarly merit of the content.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\reviews\\\\ReviewForm.tsx\",\n                                        lineNumber: 198,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        value: reviewData.contentEvaluation,\n                                        onChange: (e)=>handleInputChange(\"contentEvaluation\", e.target.value),\n                                        className: \"w-full h-40 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none text-gray-700 placeholder-gray-500\",\n                                        placeholder: \"Assess the content quality, accuracy of information, depth of analysis, relevance to the field, and contribution to existing knowledge...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\reviews\\\\ReviewForm.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\reviews\\\\ReviewForm.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-lg font-semibold text-gray-900 mb-3\",\n                                        children: \"Style & Presentation\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\reviews\\\\ReviewForm.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 mb-4\",\n                                        children: \"Evaluate writing style, organization, clarity, and presentation quality.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\reviews\\\\ReviewForm.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        value: reviewData.styleEvaluation,\n                                        onChange: (e)=>handleInputChange(\"styleEvaluation\", e.target.value),\n                                        className: \"w-full h-40 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none text-gray-700 placeholder-gray-500\",\n                                        placeholder: \"Comment on writing clarity, organization structure, flow of ideas, grammar, formatting, and overall presentation...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\reviews\\\\ReviewForm.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\reviews\\\\ReviewForm.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\reviews\\\\ReviewForm.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 11\n                    }, this),\n                    activeSection === \"feedback\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-4xl\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-gray-900 mb-6\",\n                                children: \"Detailed Feedback\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\reviews\\\\ReviewForm.tsx\",\n                                lineNumber: 235,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-lg font-semibold text-gray-900 mb-3\",\n                                        children: \"Strengths\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\reviews\\\\ReviewForm.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 mb-4\",\n                                        children: \"Highlight the major strengths and positive aspects of the manuscript.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\reviews\\\\ReviewForm.tsx\",\n                                        lineNumber: 244,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        value: reviewData.strengths,\n                                        onChange: (e)=>handleInputChange(\"strengths\", e.target.value),\n                                        className: \"w-full h-32 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none text-gray-700 placeholder-gray-500\",\n                                        placeholder: \"Note the manuscript's key strengths, innovative aspects, well-executed sections, and valuable contributions...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\reviews\\\\ReviewForm.tsx\",\n                                        lineNumber: 248,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\reviews\\\\ReviewForm.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-lg font-semibold text-gray-900 mb-3\",\n                                        children: \"Areas for Improvement\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\reviews\\\\ReviewForm.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 mb-4\",\n                                        children: \"Identify specific areas that need improvement and provide constructive suggestions.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\reviews\\\\ReviewForm.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        value: reviewData.weaknesses,\n                                        onChange: (e)=>handleInputChange(\"weaknesses\", e.target.value),\n                                        className: \"w-full h-40 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none text-gray-700 placeholder-gray-500\",\n                                        placeholder: \"Provide specific, constructive feedback on areas that need improvement, methodological concerns, gaps in analysis, or presentation issues...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\reviews\\\\ReviewForm.tsx\",\n                                        lineNumber: 265,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\reviews\\\\ReviewForm.tsx\",\n                                lineNumber: 257,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\reviews\\\\ReviewForm.tsx\",\n                        lineNumber: 234,\n                        columnNumber: 11\n                    }, this),\n                    activeSection === \"recommendation\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-4xl\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-gray-900 mb-6\",\n                                children: \"Final Recommendation\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\reviews\\\\ReviewForm.tsx\",\n                                lineNumber: 279,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-lg font-semibold text-gray-900 mb-4\",\n                                        children: \"Select your recommendation:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\reviews\\\\ReviewForm.tsx\",\n                                        lineNumber: 284,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                        children: recommendations.map((rec)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleInputChange(\"recommendation\", rec.value),\n                                                className: \"p-4 border-2 rounded-lg text-left transition-all \".concat(reviewData.recommendation === rec.value ? \"\".concat(rec.color, \" border-current\") : \"bg-white border-gray-200 hover:border-gray-300\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-semibold\",\n                                                        children: rec.label\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\reviews\\\\ReviewForm.tsx\",\n                                                        lineNumber: 300,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm mt-1 opacity-75\",\n                                                        children: [\n                                                            rec.value === \"ACCEPT\" && \"Manuscript is ready for publication\",\n                                                            rec.value === \"MINOR_REVISIONS\" && \"Small changes needed before acceptance\",\n                                                            rec.value === \"MAJOR_REVISIONS\" && \"Significant revisions required\",\n                                                            rec.value === \"REJECT\" && \"Manuscript not suitable for publication\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\reviews\\\\ReviewForm.tsx\",\n                                                        lineNumber: 301,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, rec.value, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\reviews\\\\ReviewForm.tsx\",\n                                                lineNumber: 289,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\reviews\\\\ReviewForm.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\reviews\\\\ReviewForm.tsx\",\n                                lineNumber: 283,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\reviews\\\\ReviewForm.tsx\",\n                        lineNumber: 278,\n                        columnNumber: 11\n                    }, this),\n                    activeSection === \"comments\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-4xl\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-gray-900 mb-6\",\n                                children: \"Comments\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\reviews\\\\ReviewForm.tsx\",\n                                lineNumber: 320,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-lg font-semibold text-gray-900 mb-3\",\n                                        children: \"Comments for Author\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\reviews\\\\ReviewForm.tsx\",\n                                        lineNumber: 324,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 mb-4\",\n                                        children: \"These comments will be shared with the author. Provide constructive feedback and suggestions.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\reviews\\\\ReviewForm.tsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        value: reviewData.publicComments,\n                                        onChange: (e)=>handleInputChange(\"publicComments\", e.target.value),\n                                        className: \"w-full h-40 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none text-gray-700 placeholder-gray-500\",\n                                        placeholder: \"Provide detailed feedback for the author, including specific suggestions for improvement, questions, and recommendations...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\reviews\\\\ReviewForm.tsx\",\n                                        lineNumber: 331,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\reviews\\\\ReviewForm.tsx\",\n                                lineNumber: 323,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-lg font-semibold text-gray-900 mb-3\",\n                                        children: \"Confidential Comments for Editor\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\reviews\\\\ReviewForm.tsx\",\n                                        lineNumber: 343,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 mb-4\",\n                                        children: \"These comments are only for the editor and will not be shared with the author.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\reviews\\\\ReviewForm.tsx\",\n                                        lineNumber: 346,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        value: reviewData.confidentialComments,\n                                        onChange: (e)=>handleInputChange(\"confidentialComments\", e.target.value),\n                                        className: \"w-full h-32 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none text-gray-700 placeholder-gray-500\",\n                                        placeholder: \"Share any concerns, questions, or additional context that should only be seen by the editor...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\reviews\\\\ReviewForm.tsx\",\n                                        lineNumber: 350,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\reviews\\\\ReviewForm.tsx\",\n                                lineNumber: 342,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\reviews\\\\ReviewForm.tsx\",\n                        lineNumber: 319,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\reviews\\\\ReviewForm.tsx\",\n                lineNumber: 157,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\components\\\\reviews\\\\ReviewForm.tsx\",\n        lineNumber: 77,\n        columnNumber: 5\n    }, this);\n}\n_s(ReviewForm, \"WsuqLZWTl4YmKaOztKdvQ24iQCM=\");\n_c = ReviewForm;\nvar _c;\n$RefreshReg$(_c, \"ReviewForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/components/reviews/ReviewForm.tsx\n"));

/***/ })

});