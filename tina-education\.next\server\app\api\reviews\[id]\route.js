/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/reviews/[id]/route";
exports.ids = ["app/api/reviews/[id]/route"];
exports.modules = {

/***/ "(rsc)/./app/api/reviews/[id]/route.ts":
/*!***************************************!*\
  !*** ./app/api/reviews/[id]/route.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PATCH: () => (/* binding */ PATCH)\n/* harmony export */ });\n/* harmony import */ var _auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/auth */ \"(rsc)/./auth.ts\");\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../../prisma */ \"(rsc)/./prisma.ts\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var resend__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! resend */ \"(rsc)/./node_modules/resend/dist/index.mjs\");\n\n\n\n\nconst resend = new resend__WEBPACK_IMPORTED_MODULE_3__.Resend(process.env.RESEND_API_KEY);\nasync function PATCH(request, { params }) {\n    const session = await (0,_auth__WEBPACK_IMPORTED_MODULE_0__.auth)();\n    if (!session?.user?.id) {\n        return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.json({\n            error: \"Unauthorized\"\n        }, {\n            status: 401\n        });\n    }\n    try {\n        const { action, reason } = await request.json();\n        const { id: reviewId } = await params;\n        // Validate action - for assignment response\n        if (![\n            \"ACCEPT_ASSIGNMENT\",\n            \"DECLINE_ASSIGNMENT\"\n        ].includes(action)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.json({\n                error: \"Invalid action\"\n            }, {\n                status: 400\n            });\n        }\n        // Get the review with manuscript and author details\n        const review = await _prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.review.findUnique({\n            where: {\n                id: reviewId\n            },\n            include: {\n                manuscript: {\n                    include: {\n                        user: {\n                            select: {\n                                id: true,\n                                name: true,\n                                email: true\n                            }\n                        }\n                    }\n                },\n                user: {\n                    select: {\n                        name: true,\n                        email: true\n                    }\n                }\n            }\n        });\n        if (!review) {\n            return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.json({\n                error: \"Review not found\"\n            }, {\n                status: 404\n            });\n        }\n        // Check if the current user is the assigned reviewer\n        if (review.reviewer_id !== session.user.id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.json({\n                error: \"Forbidden\"\n            }, {\n                status: 403\n            });\n        }\n        // Check if review assignment is still pending\n        if (review.status !== \"PENDING\") {\n            return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.json({\n                error: \"Review assignment has already been responded to\"\n            }, {\n                status: 400\n            });\n        }\n        // Determine new status based on action (using updated enum values)\n        const newStatus = action === \"ACCEPT_ASSIGNMENT\" ? \"ACCEPTED\" : \"DECLINED\";\n        // Update the review assignment\n        const updatedReview = await _prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.review.update({\n            where: {\n                id: reviewId\n            },\n            data: {\n                status: newStatus,\n                feedback: reason || null,\n                updatedAt: new Date()\n            }\n        });\n        const actionText = action === \"ACCEPT_ASSIGNMENT\" ? \"accepted\" : \"declined\";\n        console.log(`📝 Review assignment ${actionText} by ${session.user.name}: ${review.manuscript.title}`);\n        // Create notification for manuscript author\n        await _prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.notification.create({\n            data: {\n                userId: review.manuscript.user.id,\n                title: `Reviewer ${action === \"ACCEPT_ASSIGNMENT\" ? \"Accepted\" : \"Declined\"} Assignment`,\n                message: `${review.user.name} has ${actionText} the review assignment for \"${review.manuscript.title}\"`,\n                type: `ASSIGNMENT_${action === \"ACCEPT_ASSIGNMENT\" ? \"ACCEPTED\" : \"DECLINED\"}`,\n                relatedId: review.manuscript.id\n            }\n        });\n        console.log(`✅ Notification created for author: ${review.manuscript.user.email}`);\n        // Send email notification to manuscript author\n        try {\n            const isAccepted = action === \"ACCEPT_ASSIGNMENT\";\n            const emailResult = await resend.emails.send({\n                from: \"Tina Education <<EMAIL>>\",\n                to: review.manuscript.user.email,\n                subject: `Reviewer ${isAccepted ? \"Accepted\" : \"Declined\"} Assignment: \"${review.manuscript.title}\"`,\n                html: `\n          <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n            <div style=\"background-color: ${isAccepted ? \"#f0f9ff\" : \"#fef2f2\"}; padding: 20px; border-radius: 8px; margin-bottom: 20px;\">\n              <h2 style=\"color: ${isAccepted ? \"#1e40af\" : \"#dc2626\"}; margin: 0;\">\n                ${isAccepted ? \"✅\" : \"❌\"} Review Assignment ${isAccepted ? \"Accepted\" : \"Declined\"}\n              </h2>\n            </div>\n\n            <p>Hello <strong>${review.manuscript.user.name}</strong>,</p>\n\n            <p>We have an update regarding the review assignment for your manuscript:</p>\n\n            <div style=\"background-color: #f1f5f9; padding: 15px; border-radius: 6px; margin: 20px 0;\">\n              <h3 style=\"margin: 0 0 10px 0; color: #334155;\">📄 Assignment Details</h3>\n              <p style=\"margin: 5px 0;\"><strong>Title:</strong> ${review.manuscript.title}</p>\n              <p style=\"margin: 5px 0;\"><strong>Reviewer:</strong> ${review.user.name}</p>\n              <p style=\"margin: 5px 0;\"><strong>Status:</strong> <span style=\"color: ${isAccepted ? \"#16a34a\" : \"#dc2626\"}; font-weight: bold;\">${isAccepted ? \"ACCEPTED\" : \"DECLINED\"}</span></p>\n            </div>\n\n            ${reason ? `\n              <div style=\"background-color: #f8fafc; padding: 15px; border-radius: 6px; margin: 20px 0;\">\n                <h3 style=\"margin: 0 0 10px 0; color: #334155;\">💬 ${isAccepted ? \"Reviewer Note\" : \"Decline Reason\"}</h3>\n                <p style=\"margin: 0; line-height: 1.6;\">${reason}</p>\n              </div>\n            ` : \"\"}\n\n            ${isAccepted ? `<p><strong>Next Steps:</strong> The reviewer will now begin the review process. You will be notified when the review is completed.</p>` : `<p><strong>Next Steps:</strong> We will assign another reviewer to your manuscript. You will be notified when a new reviewer accepts the assignment.</p>`}\n\n            <div style=\"text-align: center; margin: 30px 0;\">\n              <a href=\"${process.env.NEXTAUTH_URL || \"http://localhost:3000\"}/dashboard/manuscripts\"\n                 style=\"background-color: #1e40af; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;\">\n                View My Manuscripts\n              </a>\n            </div>\n\n            <p>Thank you for using Tina Education.</p>\n\n            <hr style=\"border: none; border-top: 1px solid #e2e8f0; margin: 30px 0;\">\n            <p style=\"color: #64748b; font-size: 14px;\">\n              Best regards,<br>\n              Tina Education<br>\n              <small>This is an automated notification.</small>\n            </p>\n          </div>\n        `\n            });\n            console.log(`✅ Email sent to author: ${review.manuscript.user.email}`, emailResult);\n        } catch (emailError) {\n            console.error(`❌ Failed to send email to author:`, emailError);\n        // Don't fail the request if email fails\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.json({\n            success: true,\n            review: updatedReview,\n            message: `Assignment ${actionText} successfully`\n        });\n    } catch (error) {\n        console.error(\"Failed to update review:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_2__.NextResponse.json({\n            error: \"Internal Server Error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/reviews/[id]/route.ts\n");

/***/ }),

/***/ "(rsc)/./auth.ts":
/*!*****************!*\
  !*** ./auth.ts ***!
  \*****************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   auth: () => (/* binding */ auth),\n/* harmony export */   handlers: () => (/* binding */ handlers),\n/* harmony export */   signIn: () => (/* binding */ signIn),\n/* harmony export */   signOut: () => (/* binding */ signOut)\n/* harmony export */ });\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var _auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @auth/prisma-adapter */ \"(rsc)/./node_modules/@auth/prisma-adapter/index.js\");\n/* harmony import */ var _prisma__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/prisma */ \"(rsc)/./prisma.ts\");\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n\n\n\n\n\nconst { handlers, signIn, signOut, auth } = (0,next_auth__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    adapter: (0,_auth_prisma_adapter__WEBPACK_IMPORTED_MODULE_1__.PrismaAdapter)(_prisma__WEBPACK_IMPORTED_MODULE_2__.prisma),\n    secret: process.env.AUTH_SECRET,\n    trustHost: true,\n    pages: {\n        signIn: \"/auth/signin\"\n    },\n    providers: [\n        next_auth_providers_google__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_3__[\"default\"])({\n            credentials: {\n                username: {\n                    label: \"Username\",\n                    type: \"text\",\n                    placeholder: \"Enter UserName\"\n                },\n                password: {\n                    label: \"Password\",\n                    type: \"password\",\n                    placeholder: \"Enter Password\"\n                }\n            },\n            async authorize (credentials) {\n                const { username, password } = credentials;\n                const user = {\n                    id: \"1\",\n                    name: \"Bichesq\",\n                    email: \"<EMAIL>\"\n                };\n                if (username === user.name && password === \"nextgmail.com\") {\n                    return user;\n                } else {\n                    return null;\n                }\n            }\n        })\n    ],\n    callbacks: {\n        async authorized ({ auth, request: { nextUrl } }) {\n            // This callback is required for middleware to work properly\n            const isLoggedIn = !!auth?.user;\n            const isOnDashboard = nextUrl.pathname.startsWith(\"/dashboard\");\n            const isOnManuscripts = nextUrl.pathname.startsWith(\"/manuscripts\");\n            const isOnReviews = nextUrl.pathname.startsWith(\"/reviews\");\n            console.log(\"🔐 Authorized callback - auth:\", auth);\n            console.log(\"🔐 Authorized callback - isLoggedIn:\", isLoggedIn);\n            console.log(\"🔐 Authorized callback - pathname:\", nextUrl.pathname);\n            // Allow access to protected routes only if logged in\n            if (isOnDashboard || isOnManuscripts || isOnReviews) {\n                return isLoggedIn;\n            }\n            // Allow access to all other routes\n            return true;\n        },\n        async session ({ session, token }) {\n            console.log(\"🔐 Session callback - session:\", session);\n            console.log(\"🔐 Session callback - token:\", token);\n            if (token?.sub) {\n                session.user.id = token.sub;\n                // Fetch user role from database\n                try {\n                    const user = await _prisma__WEBPACK_IMPORTED_MODULE_2__.prisma.user.findUnique({\n                        where: {\n                            id: token.sub\n                        },\n                        select: {\n                            role: true\n                        }\n                    });\n                    if (user) {\n                        session.user.role = user.role;\n                    }\n                } catch (error) {\n                    console.error(\"Failed to fetch user role:\", error);\n                }\n            }\n            return session;\n        },\n        async jwt ({ token, user }) {\n            console.log(\"🔐 JWT callback - token:\", token);\n            console.log(\"🔐 JWT callback - user:\", user);\n            if (user) {\n                token.sub = user.id;\n            }\n            return token;\n        }\n    }\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./auth.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Freviews%2F%5Bid%5D%2Froute&page=%2Fapi%2Freviews%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Freviews%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Freviews%2F%5Bid%5D%2Froute&page=%2Fapi%2Freviews%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Freviews%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_biche_Tina_Education_org_Review_Request_backend_ARRS_NextJS_tina_education_app_api_reviews_id_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/reviews/[id]/route.ts */ \"(rsc)/./app/api/reviews/[id]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/reviews/[id]/route\",\n        pathname: \"/api/reviews/[id]\",\n        filename: \"route\",\n        bundlePath: \"app/api/reviews/[id]/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Tina Education.org\\\\Review Request backend\\\\ARRS-NextJS\\\\tina-education\\\\app\\\\api\\\\reviews\\\\[id]\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_biche_Tina_Education_org_Review_Request_backend_ARRS_NextJS_tina_education_app_api_reviews_id_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZhcGklMkZyZXZpZXdzJTJGJTVCaWQlNUQlMkZyb3V0ZSZwYWdlPSUyRmFwaSUyRnJldmlld3MlMkYlNUJpZCU1RCUyRnJvdXRlJmFwcFBhdGhzPSZwYWdlUGF0aD1wcml2YXRlLW5leHQtYXBwLWRpciUyRmFwaSUyRnJldmlld3MlMkYlNUJpZCU1RCUyRnJvdXRlLnRzJmFwcERpcj1DJTNBJTVDVXNlcnMlNUNiaWNoZSU1Q1RpbmElMjBFZHVjYXRpb24ub3JnJTVDUmV2aWV3JTIwUmVxdWVzdCUyMGJhY2tlbmQlNUNBUlJTLU5leHRKUyU1Q3RpbmEtZWR1Y2F0aW9uJTVDYXBwJnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMmcm9vdERpcj1DJTNBJTVDVXNlcnMlNUNiaWNoZSU1Q1RpbmElMjBFZHVjYXRpb24ub3JnJTVDUmV2aWV3JTIwUmVxdWVzdCUyMGJhY2tlbmQlNUNBUlJTLU5leHRKUyU1Q3RpbmEtZWR1Y2F0aW9uJmlzRGV2PXRydWUmdHNjb25maWdQYXRoPXRzY29uZmlnLmpzb24mYmFzZVBhdGg9JmFzc2V0UHJlZml4PSZuZXh0Q29uZmlnT3V0cHV0PSZwcmVmZXJyZWRSZWdpb249Jm1pZGRsZXdhcmVDb25maWc9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7OztBQUErRjtBQUN2QztBQUNxQjtBQUM0RTtBQUN6SjtBQUNBO0FBQ0E7QUFDQSx3QkFBd0IseUdBQW1CO0FBQzNDO0FBQ0EsY0FBYyxrRUFBUztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsWUFBWTtBQUNaLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQSxRQUFRLHNEQUFzRDtBQUM5RDtBQUNBLFdBQVcsNEVBQVc7QUFDdEI7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUMwRjs7QUFFMUYiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBBcHBSb3V0ZVJvdXRlTW9kdWxlIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcm91dGUtbW9kdWxlcy9hcHAtcm91dGUvbW9kdWxlLmNvbXBpbGVkXCI7XG5pbXBvcnQgeyBSb3V0ZUtpbmQgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yb3V0ZS1raW5kXCI7XG5pbXBvcnQgeyBwYXRjaEZldGNoIGFzIF9wYXRjaEZldGNoIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvbGliL3BhdGNoLWZldGNoXCI7XG5pbXBvcnQgKiBhcyB1c2VybGFuZCBmcm9tIFwiQzpcXFxcVXNlcnNcXFxcYmljaGVcXFxcVGluYSBFZHVjYXRpb24ub3JnXFxcXFJldmlldyBSZXF1ZXN0IGJhY2tlbmRcXFxcQVJSUy1OZXh0SlNcXFxcdGluYS1lZHVjYXRpb25cXFxcYXBwXFxcXGFwaVxcXFxyZXZpZXdzXFxcXFtpZF1cXFxccm91dGUudHNcIjtcbi8vIFdlIGluamVjdCB0aGUgbmV4dENvbmZpZ091dHB1dCBoZXJlIHNvIHRoYXQgd2UgY2FuIHVzZSB0aGVtIGluIHRoZSByb3V0ZVxuLy8gbW9kdWxlLlxuY29uc3QgbmV4dENvbmZpZ091dHB1dCA9IFwiXCJcbmNvbnN0IHJvdXRlTW9kdWxlID0gbmV3IEFwcFJvdXRlUm91dGVNb2R1bGUoe1xuICAgIGRlZmluaXRpb246IHtcbiAgICAgICAga2luZDogUm91dGVLaW5kLkFQUF9ST1VURSxcbiAgICAgICAgcGFnZTogXCIvYXBpL3Jldmlld3MvW2lkXS9yb3V0ZVwiLFxuICAgICAgICBwYXRobmFtZTogXCIvYXBpL3Jldmlld3MvW2lkXVwiLFxuICAgICAgICBmaWxlbmFtZTogXCJyb3V0ZVwiLFxuICAgICAgICBidW5kbGVQYXRoOiBcImFwcC9hcGkvcmV2aWV3cy9baWRdL3JvdXRlXCJcbiAgICB9LFxuICAgIHJlc29sdmVkUGFnZVBhdGg6IFwiQzpcXFxcVXNlcnNcXFxcYmljaGVcXFxcVGluYSBFZHVjYXRpb24ub3JnXFxcXFJldmlldyBSZXF1ZXN0IGJhY2tlbmRcXFxcQVJSUy1OZXh0SlNcXFxcdGluYS1lZHVjYXRpb25cXFxcYXBwXFxcXGFwaVxcXFxyZXZpZXdzXFxcXFtpZF1cXFxccm91dGUudHNcIixcbiAgICBuZXh0Q29uZmlnT3V0cHV0LFxuICAgIHVzZXJsYW5kXG59KTtcbi8vIFB1bGwgb3V0IHRoZSBleHBvcnRzIHRoYXQgd2UgbmVlZCB0byBleHBvc2UgZnJvbSB0aGUgbW9kdWxlLiBUaGlzIHNob3VsZFxuLy8gYmUgZWxpbWluYXRlZCB3aGVuIHdlJ3ZlIG1vdmVkIHRoZSBvdGhlciByb3V0ZXMgdG8gdGhlIG5ldyBmb3JtYXQuIFRoZXNlXG4vLyBhcmUgdXNlZCB0byBob29rIGludG8gdGhlIHJvdXRlLlxuY29uc3QgeyB3b3JrQXN5bmNTdG9yYWdlLCB3b3JrVW5pdEFzeW5jU3RvcmFnZSwgc2VydmVySG9va3MgfSA9IHJvdXRlTW9kdWxlO1xuZnVuY3Rpb24gcGF0Y2hGZXRjaCgpIHtcbiAgICByZXR1cm4gX3BhdGNoRmV0Y2goe1xuICAgICAgICB3b3JrQXN5bmNTdG9yYWdlLFxuICAgICAgICB3b3JrVW5pdEFzeW5jU3RvcmFnZVxuICAgIH0pO1xufVxuZXhwb3J0IHsgcm91dGVNb2R1bGUsIHdvcmtBc3luY1N0b3JhZ2UsIHdvcmtVbml0QXN5bmNTdG9yYWdlLCBzZXJ2ZXJIb29rcywgcGF0Y2hGZXRjaCwgIH07XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWFwcC1yb3V0ZS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Freviews%2F%5Bid%5D%2Froute&page=%2Fapi%2Freviews%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Freviews%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./prisma.ts":
/*!*******************!*\
  !*** ./prisma.ts ***!
  \*******************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma || new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9wcmlzbWEudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQThDO0FBRTlDLE1BQU1DLGtCQUFrQkM7QUFFakIsTUFBTUMsU0FBU0YsZ0JBQWdCRSxNQUFNLElBQUksSUFBSUgsd0RBQVlBLEdBQUc7QUFFbkUsSUFBSUksSUFBcUMsRUFBRUgsZ0JBQWdCRSxNQUFNLEdBQUdBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGJpY2hlXFxUaW5hIEVkdWNhdGlvbi5vcmdcXFJldmlldyBSZXF1ZXN0IGJhY2tlbmRcXEFSUlMtTmV4dEpTXFx0aW5hLWVkdWNhdGlvblxccHJpc21hLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFByaXNtYUNsaWVudCB9IGZyb20gXCJAcHJpc21hL2NsaWVudFwiO1xyXG5cclxuY29uc3QgZ2xvYmFsRm9yUHJpc21hID0gZ2xvYmFsVGhpcyBhcyB1bmtub3duIGFzIHsgcHJpc21hOiBQcmlzbWFDbGllbnQgfTtcclxuXHJcbmV4cG9ydCBjb25zdCBwcmlzbWEgPSBnbG9iYWxGb3JQcmlzbWEucHJpc21hIHx8IG5ldyBQcmlzbWFDbGllbnQoKTtcclxuXHJcbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gXCJwcm9kdWN0aW9uXCIpIGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPSBwcmlzbWE7XHJcbiJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJnbG9iYWxGb3JQcmlzbWEiLCJnbG9iYWxUaGlzIiwicHJpc21hIiwicHJvY2VzcyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./prisma.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../../server/app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "async_hooks":
/*!******************************!*\
  !*** external "async_hooks" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("async_hooks");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:stream");

/***/ }),

/***/ "prettier/plugins/html":
/*!****************************************!*\
  !*** external "prettier/plugins/html" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = import("prettier/plugins/html");;

/***/ }),

/***/ "prettier/standalone":
/*!**************************************!*\
  !*** external "prettier/standalone" ***!
  \**************************************/
/***/ ((module) => {

"use strict";
module.exports = import("prettier/standalone");;

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@auth","vendor-chunks/next-auth","vendor-chunks/oauth4webapi","vendor-chunks/jose","vendor-chunks/preact","vendor-chunks/preact-render-to-string","vendor-chunks/@panva","vendor-chunks/resend"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Freviews%2F%5Bid%5D%2Froute&page=%2Fapi%2Freviews%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Freviews%2F%5Bid%5D%2Froute.ts&appDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cbiche%5CTina%20Education.org%5CReview%20Request%20backend%5CARRS-NextJS%5Ctina-education&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();