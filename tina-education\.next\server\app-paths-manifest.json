{"/api/auth/[...nextauth]/route": "app/api/auth/[...nextauth]/route.js", "/api/auth/notifications/route": "app/api/auth/notifications/route.js", "/api/manuscripts/route": "app/api/manuscripts/route.js", "/repository/page": "app/repository/page.js", "/repository/[id]/page": "app/repository/[id]/page.js", "/manuscripts/new/page": "app/manuscripts/new/page.js", "/manuscripts/[id]/page": "app/manuscripts/[id]/page.js", "/dashboard/page": "app/dashboard/page.js", "/dashboard/library/page": "app/dashboard/library/page.js"}